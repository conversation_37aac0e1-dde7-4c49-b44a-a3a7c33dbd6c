#!/usr/bin/env node

/**
 * Test script to verify service-specific clientId functionality
 * This script tests that OMS service calls use 'orders-portal' as clientId
 */

import fetch from 'node-fetch';

const MCP_SERVER_URL = 'http://localhost:6300/mcp';
const SESSION_ID = 'test-session-' + Math.random().toString(36).substring(2, 15);

async function testMCPCall() {
  console.log('🧪 Testing Service-Specific ClientId Functionality');
  console.log('='.repeat(60));
  console.log(`📡 Server: ${MCP_SERVER_URL}`);
  console.log(`🔑 Session: ${SESSION_ID}`);
  console.log('');

  try {
    // Step 1: Initialize MCP session
    console.log('📡 Step 1: MCP Initialize');
    const initResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
          },
          clientInfo: {
            name: 'test-client',
            version: '1.0.0',
          },
        },
      }),
    });

    const initResult = await initResponse.json();
    console.log(`✅ Initialize: ${initResponse.status}`);
    console.log(`   Protocol: ${initResult.result?.protocolVersion}`);
    console.log(
      `   Server: ${initResult.result?.serverInfo?.name} v${initResult.result?.serverInfo?.version}`
    );
    console.log('');

    // Step 2: List tools
    console.log('🔧 Step 2: List Tools');
    const toolsResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/list',
        params: {},
      }),
    });

    const toolsResult = await toolsResponse.json();
    console.log(`✅ Tools listed: ${toolsResponse.status}`);
    const omsTools = toolsResult.result?.tools?.filter(t => t.name.startsWith('oms_')) || [];
    console.log(`   Found ${omsTools.length} OMS tools:`);
    omsTools.forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description}`);
    });
    console.log('');

    // Step 3: Test OMS service call (this should use 'orders-portal' clientId)
    console.log('🏪 Step 3: Test OMS Service Call');
    console.log('   This should trigger service-specific clientId: orders-portal');

    const omsResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'oms_queryOrderLists',
          arguments: {
            page: 1,
            pageSize: 5,
          },
        },
      }),
    });

    const omsResult = await omsResponse.json();
    console.log(`📊 OMS Query Result: ${omsResponse.status}`);

    if (omsResult.error) {
      console.log(`❌ Error: ${omsResult.error.message}`);
      console.log(`   Code: ${omsResult.error.code}`);
      if (omsResult.error.data) {
        console.log(`   Details:`, JSON.stringify(omsResult.error.data, null, 2));
      }
    } else {
      console.log(`✅ Success: OMS service call completed`);
      const content = omsResult.result?.content?.[0]?.text;
      if (content) {
        try {
          const parsed = JSON.parse(content);
          console.log(`   Success: ${parsed.success}`);
          console.log(`   Module: ${parsed.meta?.module}`);
          console.log(`   Function: ${parsed.meta?.function}`);
        } catch (e) {
          console.log(`   Raw response: ${content.substring(0, 200)}...`);
        }
      }
    }

    console.log('');
    console.log('🔍 Check server logs for:');
    console.log('   - "Using service-specific clientId: orders-portal"');
    console.log('   - "McpUserToken exchange with clientId: orders-portal"');
    console.log('   - Service module: oms');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.cause) {
      console.error('   Cause:', error.cause);
    }
  }
}

// Run the test
testMCPCall().catch(console.error);
