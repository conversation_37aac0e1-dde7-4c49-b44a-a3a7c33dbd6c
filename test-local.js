#!/usr/bin/env node

/**
 * Simple test to verify the enhanced logging for service-specific clientId
 */

import fetch from 'node-fetch';

const MCP_SERVER_URL = 'http://localhost:6300/mcp';
const SESSION_ID = 'test-session-' + Math.random().toString(36).substring(2, 15);

async function testMCPCall() {
  console.log('🧪 Testing Enhanced Permission Service Logging');
  console.log('='.repeat(60));
  console.log(`📡 Server: ${MCP_SERVER_URL}`);
  console.log(`🔑 Session: ${SESSION_ID}`);
  console.log('');

  try {
    // Step 1: Initialize MCP session
    console.log('📡 Step 1: MCP Initialize');
    const initResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: { tools: {} },
          clientInfo: { name: 'test-client', version: '1.0.0' },
        },
      }),
    });

    const initResult = await initResponse.json();
    console.log(`✅ Initialize: ${initResponse.status}`);
    if (initResult.result) {
      console.log(`   Protocol: ${initResult.result.protocolVersion}`);
      console.log(
        `   Server: ${initResult.result.serverInfo?.name} v${initResult.result.serverInfo?.version}`
      );
    }
    console.log('');

    // Step 2: Test OMS service call to trigger service-specific clientId
    console.log('🏪 Step 2: Test OMS Service Call (should use orders-portal clientId)');

    const omsResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: 'oms_queryOrderLists',
          arguments: { page: 1, pageSize: 5 },
        },
      }),
    });

    const omsResult = await omsResponse.json();
    console.log(`📊 OMS Query Result: ${omsResponse.status}`);

    if (omsResult.error) {
      console.log(`❌ Error: ${omsResult.error.message}`);
      console.log(`   Code: ${omsResult.error.code}`);
    } else {
      console.log(`✅ Success: OMS service call completed`);
    }

    console.log('');
    console.log('🔍 Check server logs above for:');
    console.log('   - "Using service-specific clientId for token exchange"');
    console.log('   - "serviceSpecificClientId: orders-portal"');
    console.log('   - "Permission service HTTP request" details');
    console.log('   - "Permission service response" details');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testMCPCall().catch(console.error);
