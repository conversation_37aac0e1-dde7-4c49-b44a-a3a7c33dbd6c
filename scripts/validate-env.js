#!/usr/bin/env node

/**
 * Environment Validation Script
 *
 * This script validates the environment configuration and provides
 * security recommendations for the MCP Server ODI.
 *
 * Usage:
 *   node scripts/validate-env.js
 *   npm run validate:env
 */

import { readFileSync, existsSync } from 'fs';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = resolve(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function loadEnvFile(filePath) {
  if (!existsSync(filePath)) {
    return null;
  }

  const content = readFileSync(filePath, 'utf8');
  const env = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return env;
}

function validateUrl(url, name) {
  try {
    const parsed = new URL(url);
    if (parsed.protocol !== 'https:' && process.env.NODE_ENV === 'production') {
      return { valid: false, message: `${name} should use HTTPS in production` };
    }
    return { valid: true };
  } catch {
    return { valid: false, message: `${name} is not a valid URL` };
  }
}

function validatePort(port, name) {
  const portNum = parseInt(port, 10);
  if (isNaN(portNum) || portNum < 1024 || portNum > 65535) {
    return { valid: false, message: `${name} should be between 1024 and 65535` };
  }
  return { valid: true };
}

function validateEnvironment(env) {
  const results = {
    errors: [],
    warnings: [],
    security: [],
    info: [],
  };

  // Required variables check
  const requiredVars = ['VITE_API_HOST_KONG'];

  if (!env.AUTH_COOKIES && env.OAUTH2_ENABLED !== 'true') {
    requiredVars.push('AUTH_COOKIES');
  }

  if (env.OAUTH2_ENABLED === 'true') {
    // Note: OAUTH2_CLIENT_ID removed - now provided by clients in requests
    requiredVars.push('KEYCLOAK_BASE_URL', 'KEYCLOAK_REALM');
  }

  requiredVars.forEach(varName => {
    if (!env[varName]) {
      results.errors.push(`Missing required variable: ${varName}`);
    }
  });

  // URL validation
  if (env.VITE_API_HOST) {
    const urlCheck = validateUrl(env.VITE_API_HOST, 'VITE_API_HOST');
    if (!urlCheck.valid) results.errors.push(urlCheck.message);
  }

  if (env.VITE_API_HOST_KONG) {
    const urlCheck = validateUrl(env.VITE_API_HOST_KONG, 'VITE_API_HOST_KONG');
    if (!urlCheck.valid) results.errors.push(urlCheck.message);
  }

  if (env.KEYCLOAK_BASE_URL) {
    const urlCheck = validateUrl(env.KEYCLOAK_BASE_URL, 'KEYCLOAK_BASE_URL');
    if (!urlCheck.valid) results.errors.push(urlCheck.message);
  }

  // Port validation
  if (env.MCP_SERVER_PORT) {
    const portCheck = validatePort(env.MCP_SERVER_PORT, 'MCP_SERVER_PORT');
    if (!portCheck.valid) results.warnings.push(portCheck.message);
  }

  // Security checks
  if (env.NODE_ENV === 'production') {
    if (env.DEBUG_SERVICE_ADAPTER === 'true') {
      results.security.push('DEBUG_SERVICE_ADAPTER should be false in production');
    }

    if (env.DEBUG_HTTP_REQUESTS === 'true') {
      results.security.push('DEBUG_HTTP_REQUESTS should be false in production');
    }

    if (env.DEBUG_OAUTH2 === 'true') {
      results.security.push('DEBUG_OAUTH2 should be false in production');
    }

    if (env.CONNECTION_AUTH_ENABLED !== 'true') {
      results.security.push('CONNECTION_AUTH_ENABLED should be true in production');
    }

    if (env.CONNECTION_AUTH_STRICT !== 'true') {
      results.security.push('CONNECTION_AUTH_STRICT should be true in production');
    }
  }

  // Transport-specific warnings
  if (env.TRANSPORT === 'stdio' && env.MCP_SERVER_PORT && env.MCP_SERVER_PORT !== '3000') {
    results.warnings.push('MCP_SERVER_PORT is ignored in stdio transport mode');
  }

  // OAuth2 configuration warnings
  if (env.OAUTH2_ENABLED === 'true') {
    if (!env.OAUTH2_CLIENT_SECRET || env.OAUTH2_CLIENT_SECRET.length < 32) {
      results.security.push('OAUTH2_CLIENT_SECRET should be at least 32 characters long');
    }

    if (env.OAUTH2_CLIENT_SECRET === 'your-client-secret') {
      results.errors.push('OAUTH2_CLIENT_SECRET must be changed from default value');
    }
  }

  // API key warnings
  if (env.VITE_MASTER_DATA_API_KEY === 'your-master-data-api-key') {
    results.errors.push('VITE_MASTER_DATA_API_KEY must be changed from default value');
  }

  // Info messages
  results.info.push(`Environment: ${env.NODE_ENV || 'development'}`);
  results.info.push(`Transport: ${env.TRANSPORT || 'stdio'}`);
  results.info.push(`OAuth2: ${env.OAUTH2_ENABLED === 'true' ? 'enabled' : 'disabled'}`);
  results.info.push(`Port: ${env.MCP_SERVER_PORT || '3000'}`);

  return results;
}

function printResults(results) {
  console.log(colorize('\n🔧 MCP Server ODI - Environment Validation', 'bold'));
  console.log('='.repeat(50));

  // Info
  if (results.info.length > 0) {
    console.log(colorize('\n📋 Configuration:', 'blue'));
    results.info.forEach(info => console.log(`  ℹ️  ${info}`));
  }

  // Errors
  if (results.errors.length > 0) {
    console.log(colorize('\n❌ Errors (must fix):', 'red'));
    results.errors.forEach(error => console.log(`  ❌ ${error}`));
  }

  // Security issues
  if (results.security.length > 0) {
    console.log(colorize('\n🔒 Security Recommendations:', 'magenta'));
    results.security.forEach(security => console.log(`  🔒 ${security}`));
  }

  // Warnings
  if (results.warnings.length > 0) {
    console.log(colorize('\n⚠️  Warnings:', 'yellow'));
    results.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  const hasErrors = results.errors.length > 0;
  const hasSecurityIssues = results.security.length > 0;

  if (hasErrors) {
    console.log(colorize('❌ Validation FAILED - fix errors before proceeding', 'red'));
    console.log(colorize('💡 Tip: Copy .env.example to .env and update with your values', 'cyan'));
  } else if (hasSecurityIssues) {
    console.log(colorize('⚠️  Validation PASSED with security recommendations', 'yellow'));
    console.log(colorize('🔒 Consider addressing security issues for production', 'cyan'));
  } else {
    console.log(colorize('✅ Validation PASSED - configuration looks good!', 'green'));
  }

  console.log(colorize('\n📚 Documentation:', 'cyan'));
  console.log('  - docs/ENVIRONMENT_GUIDE.md');
  console.log('  - docs/SECURITY_BEST_PRACTICES.md');
  console.log('  - .env.example (template)');
}

function main() {
  const envPath = resolve(rootDir, '.env');
  const env = loadEnvFile(envPath);

  if (!env) {
    console.log(colorize('❌ No .env file found', 'red'));
    console.log(colorize('💡 Copy .env.example to .env and update with your values', 'cyan'));
    console.log(`   cp ${resolve(rootDir, '.env.example')} ${envPath}`);
    process.exit(1);
  }

  const results = validateEnvironment(env);
  printResults(results);

  // Exit with error code if validation failed
  if (results.errors.length > 0) {
    process.exit(1);
  }
}

main();
