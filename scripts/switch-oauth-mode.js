#!/usr/bin/env node

/**
 * Script to switch between OAuth2 authentication modes
 * Usage: node scripts/switch-oauth-mode.js [public|confidential]
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const envPath = path.join(__dirname, '..', '.env');

const configurations = {
  public: {
    OAUTH2_CLIENT_ID: 'mcp-public-client',
    OAUTH2_CLIENT_SECRET: '',
    OAUTH2_CLIENT_AUTH_METHOD: 'none',
    description: 'Public client configuration (for mcp-inspector)'
  },
  confidential: {
    OAUTH2_CLIENT_ID: 'permission-service',
    OAUTH2_CLIENT_SECRET: 'cscDmucuSZ5w2tAshjbQC9r0IWPPiL9X',
    OAUTH2_CLIENT_AUTH_METHOD: 'client_secret_basic',
    description: 'Confidential client configuration (production)'
  }
};

function updateEnvFile(mode) {
  if (!configurations[mode]) {
    console.error(`❌ Invalid mode: ${mode}`);
    console.error('Available modes: public, confidential');
    process.exit(1);
  }

  const config = configurations[mode];
  
  try {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update each configuration value
    Object.entries(config).forEach(([key, value]) => {
      if (key === 'description') return;
      
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const replacement = `${key}=${value}`;
      
      if (envContent.match(regex)) {
        envContent = envContent.replace(regex, replacement);
      } else {
        // If the key doesn't exist, add it after the OAuth2 section
        const insertAfter = '# OAuth2 client credentials';
        const insertIndex = envContent.indexOf(insertAfter);
        if (insertIndex !== -1) {
          const lineEnd = envContent.indexOf('\n', insertIndex);
          envContent = envContent.slice(0, lineEnd + 1) + `${replacement}\n` + envContent.slice(lineEnd + 1);
        }
      }
    });
    
    fs.writeFileSync(envPath, envContent);
    
    console.log(`✅ Switched to ${mode} mode: ${config.description}`);
    console.log('📋 Configuration:');
    Object.entries(config).forEach(([key, value]) => {
      if (key === 'description') return;
      if (key === 'OAUTH2_CLIENT_SECRET' && value) {
        console.log(`  ${key}: ***SET***`);
      } else {
        console.log(`  ${key}: ${value || '(empty)'}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error updating .env file:', error.message);
    process.exit(1);
  }
}

function showCurrentMode() {
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const authMethod = envContent.match(/^OAUTH2_CLIENT_AUTH_METHOD=(.*)$/m)?.[1] || 'client_secret_basic';
    const clientId = envContent.match(/^OAUTH2_CLIENT_ID=(.*)$/m)?.[1] || 'unknown';
    
    console.log('🔍 Current OAuth2 configuration:');
    console.log(`  Client ID: ${clientId}`);
    console.log(`  Auth Method: ${authMethod}`);
    
    const mode = authMethod === 'none' ? 'public' : 'confidential';
    console.log(`  Mode: ${mode} (${configurations[mode].description})`);
    
  } catch (error) {
    console.error('❌ Error reading .env file:', error.message);
  }
}

// Main execution
const mode = process.argv[2];

if (!mode) {
  console.log('🔧 OAuth2 Authentication Mode Switcher');
  console.log('');
  showCurrentMode();
  console.log('');
  console.log('Usage: node scripts/switch-oauth-mode.js [public|confidential]');
  console.log('');
  console.log('Modes:');
  Object.entries(configurations).forEach(([key, config]) => {
    console.log(`  ${key}: ${config.description}`);
  });
} else {
  updateEnvFile(mode);
}
