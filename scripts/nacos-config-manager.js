#!/usr/bin/env node

/**
 * NACOS Configuration Validator
 *
 * This script validates NACOS configuration for the MCP Server ODI.
 * Configuration is managed manually through the Aliyun portal.
 *
 * Usage:
 *   node scripts/nacos-config-manager.js validate
 *   node scripts/nacos-config-manager.js test-connection
 */

import { NacosConfigClient } from 'nacos';
import * as yaml from 'js-yaml';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
  console.log(colorize('\n🔧 NACOS Configuration Manager', 'cyan'));
  console.log(colorize('=====================================', 'cyan'));
}

function printSuccess(message) {
  console.log(colorize(`✅ ${message}`, 'green'));
}

function printError(message) {
  console.log(colorize(`❌ ${message}`, 'red'));
}

function printWarning(message) {
  console.log(colorize(`⚠️  ${message}`, 'yellow'));
}

function printInfo(message) {
  console.log(colorize(`ℹ️  ${message}`, 'blue'));
}

class NacosConfigValidator {
  constructor() {
    this.dataId = process.env.NACOS_DATA_ID || 'com.ikea.mpp:mcp-server-odi-dev';
    this.group = process.env.NACOS_GROUP || 'DEFAULT_GROUP';
    this.client = null;
    this.configFormat = process.env.NACOS_CONFIG_FORMAT || 'yaml';
  }

  /**
   * Parse configuration content based on format (JSON or YAML)
   */
  parseConfigContent(content) {
    try {
      if (this.configFormat.toLowerCase() === 'yaml' || this.configFormat.toLowerCase() === 'yml') {
        printInfo('Parsing YAML configuration...');
        return yaml.load(content);
      } else {
        printInfo('Parsing JSON configuration...');
        return JSON.parse(content);
      }
    } catch (error) {
      printError(`Failed to parse ${this.configFormat.toUpperCase()} configuration: ${error.message}`);
      // Try alternative format as fallback
      try {
        if (this.configFormat.toLowerCase() === 'yaml') {
          printInfo('Falling back to JSON parsing...');
          return JSON.parse(content);
        } else {
          printInfo('Falling back to YAML parsing...');
          return yaml.load(content);
        }
      } catch (fallbackError) {
        printError('Both JSON and YAML parsing failed');
        throw new Error(`Failed to parse configuration content in both JSON and YAML formats`);
      }
    }
  }

  async initializeClient() {
    try {
      const serverAddr = process.env.NACOS_SERVER_ADDR;
      const namespace = process.env.NACOS_NAMESPACE;
      const accessKey = process.env.NACOS_ACCESS_KEY;
      const secretKey = process.env.NACOS_SECRET_KEY;

      if (!serverAddr || !namespace || !accessKey || !secretKey) {
        printError('Missing NACOS environment variables:');
        console.log('  NACOS_SERVER_ADDR:', serverAddr || 'NOT SET');
        console.log('  NACOS_NAMESPACE:', namespace || 'NOT SET');
        console.log('  NACOS_ACCESS_KEY:', accessKey ? 'SET' : 'NOT SET');
        console.log('  NACOS_SECRET_KEY:', secretKey ? 'SET' : 'NOT SET');
        return false;
      }

      this.client = new NacosConfigClient({
        serverAddr,
        namespace,
        accessKey,
        secretKey,
      });

      printSuccess('NACOS client initialized successfully');
      return true;
    } catch (error) {
      printError(`Failed to initialize NACOS client: ${error.message}`);
      return false;
    }
  }

  async testConnection() {
    printHeader();
    printInfo('Testing NACOS connection...');

    if (!await this.initializeClient()) {
      process.exit(1);
    }

    try {
      // Try to get configuration to test connection
      printInfo(`Testing connection to NACOS server...`);
      const configContent = await this.client.getConfig(this.dataId, this.group);

      if (configContent) {
        printSuccess('NACOS connection successful!');
        printInfo(`Configuration found for ${this.dataId}`);

        const config = JSON.parse(configContent);
        printInfo(`Configuration keys: ${Object.keys(config).join(', ')}`);
      } else {
        printWarning(`NACOS connection successful, but no configuration found for ${this.dataId}`);
        printInfo('You can add configuration through the Aliyun portal');
      }
    } catch (error) {
      printError(`NACOS connection failed: ${error.message}`);
      process.exit(1);
    }
  }

  async validateConfig() {
    printHeader();
    printInfo('Validating NACOS configuration...');

    if (!await this.initializeClient()) {
      process.exit(1);
    }

    try {
      // Check if config exists in NACOS
      const nacosConfig = await this.client.getConfig(this.dataId, this.group);
      
      if (!nacosConfig) {
        printWarning('No configuration found in NACOS');
        printInfo('Run: node scripts/nacos-config-manager.js upload');
        return;
      }

      const config = this.parseConfigContent(nacosConfig);
      
      // Validate required fields
      const requiredFields = [
        'VITE_API_HOST',
        'VITE_API_HOST_KONG',
        'VITE_MASTER_DATA_API_HOST'
      ];

      let isValid = true;
      
      for (const field of requiredFields) {
        if (!config[field]) {
          printError(`Missing required field: ${field}`);
          isValid = false;
        } else {
          printSuccess(`✓ ${field}: ${config[field]}`);
        }
      }

      // Configuration validation passed
      printInfo('All required configuration keys are present in NACOS');

      if (isValid) {
        printSuccess('NACOS configuration validation passed! ✨');
      } else {
        printError('NACOS configuration validation failed');
        process.exit(1);
      }
    } catch (error) {
      printError(`Error validating configuration: ${error.message}`);
      process.exit(1);
    }
  }

  showHelp() {
    printHeader();
    console.log('Validate NACOS configuration for MCP Server ODI\n');
    console.log('Commands:');
    console.log('  validate        Validate configuration in NACOS');
    console.log('  test-connection Test connection to NACOS server');
    console.log('  help            Show this help message\n');
    console.log('Environment Variables Required:');
    console.log('  NACOS_SERVER_ADDR    NACOS server address');
    console.log('  NACOS_NAMESPACE      NACOS namespace');
    console.log('  NACOS_ACCESS_KEY     NACOS access key');
    console.log('  NACOS_SECRET_KEY     NACOS secret key\n');
    console.log('Configuration Management:');
    console.log('  Configuration is managed manually through the Aliyun portal');
    console.log('  DataId: com.ikea.mpp:mcp-server-odi-dev (configurable via NACOS_DATA_ID)');
    console.log('  Group: DEFAULT_GROUP (configurable via NACOS_GROUP)\n');
    console.log('Example:');
    console.log('  export NACOS_SERVER_ADDR=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848');
    console.log('  export NACOS_NAMESPACE=8bc453ff-d8df-4ee3-acf4-4de86c19f955');
    console.log('  export NACOS_ACCESS_KEY=your-access-key');
    console.log('  export NACOS_SECRET_KEY=your-secret-key');
    console.log('  node scripts/nacos-config-manager.js validate');
  }
}

async function main() {
  const validator = new NacosConfigValidator();
  const command = process.argv[2];

  switch (command) {
    case 'validate':
      await validator.validateConfig();
      break;
    case 'test-connection':
    case 'test':
      await validator.testConnection();
      break;
    case 'help':
    case '--help':
    case '-h':
      validator.showHelp();
      break;
    default:
      printError(`Unknown command: ${command || 'none'}`);
      validator.showHelp();
      process.exit(1);
  }
}

main().catch(error => {
  printError(`Script failed: ${error.message}`);
  process.exit(1);
});
