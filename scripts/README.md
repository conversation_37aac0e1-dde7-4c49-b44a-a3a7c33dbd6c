# MCP Server Scripts

This directory contains utility scripts for OAuth2/Keycloak development and testing.

## Scripts Overview

### 🔐 `test-keycloak.js` (296 lines)

**Purpose**: Test and configure Keycloak connectivity for OAuth2 authentication

**Features**:

- Tests multiple Keycloak configurations (local/remote)
- Validates OAuth2 endpoints availability
- Generates environment configuration
- Tests MCP server OAuth2 integration

**Usage**:

```bash
# Test Keycloak connectivity
npm run keycloak:test

# Test and apply configuration
npm run keycloak:configure
```

### 🐳 `setup-local-keycloak.sh` (309 lines)

**Purpose**: Set up and manage local Keycloak instance for development

**Features**:

- Starts/stops local Keycloak instance
- Configures realm and clients automatically
- Provides status monitoring and logs
- Standalone setup script

**Note**: Docker-related npm scripts have been removed. Use the script directly if needed.

## When to Use These Scripts

### ✅ Keep scripts if you:

- Use OAuth2 authentication (`OAUTH2_ENABLED=true`)
- Develop with Keycloak integration
- Need local OAuth2 testing environment
- Work with HTTP transport mode

### ❌ Consider removing if you:

- Only use cookie-based authentication
- Never use OAuth2 features
- Only use stdio transport mode
- Want minimal codebase

## Current Status

Based on your `.env` file:

- ✅ OAuth2 is **enabled** (`OAUTH2_ENABLED=true`)
- ✅ Keycloak configuration is **present**
- ✅ HTTP transport is **active** (`TRANSPORT=http`)
- ✅ Scripts are **actively used** by npm scripts

## Alternative: Minimal Cleanup

If you want to keep OAuth2 but reduce script complexity, you could:

1. **Keep the scripts** (recommended) - They're useful for OAuth2 development
2. **Document usage** - Add this README for clarity
3. **Simplify if needed** - Remove unused configurations

## Related Files

- `.env` - OAuth2 configuration variables
- `src/auth/` - OAuth2 implementation code
- `docs/OAUTH2_GUIDE.md` - OAuth2 setup documentation

## Recommendation

**Keep the scripts folder** since:

- OAuth2 is actively enabled in your configuration
- The scripts provide valuable development tools
- They're referenced by multiple npm scripts
- Removing them would break OAuth2 development workflow

The scripts are well-organized and serve a specific purpose for OAuth2/Keycloak development.
