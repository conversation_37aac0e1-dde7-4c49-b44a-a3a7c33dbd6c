#!/usr/bin/env node

/**
 * Authentication Configuration Switcher
 * 
 * Quickly switch between OAuth2 enabled/disabled configurations
 * 
 * Usage:
 *   node scripts/switch-auth-config.js --oauth2-enabled
 *   node scripts/switch-auth-config.js --oauth2-disabled
 *   node scripts/switch-auth-config.js --help
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve } from 'path';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

const OAUTH2_ENABLED_CONFIG = {
  OAUTH2_ENABLED: 'true',
  CONNECTION_AUTH_ENABLED: 'true',
  CONNECTION_AUTH_STRICT: 'true',
  CONNECTION_AUTH_TEST_API: 'false',
  PER_REQUEST_AUTH_ENABLED: 'true',
  PER_REQUEST_AUTH_CACHE_ENABLED: 'true',
  PER_REQUEST_AUTH_CACHE_MAX_AGE: '300',
  PER_REQUEST_AUTH_LOG_VALIDATION: 'false'
};

const OAUTH2_DISABLED_CONFIG = {
  OAUTH2_ENABLED: 'false',
  CONNECTION_AUTH_ENABLED: 'false',
  CONNECTION_AUTH_STRICT: 'false',
  CONNECTION_AUTH_TEST_API: 'false',
  PER_REQUEST_AUTH_ENABLED: 'false',
  PER_REQUEST_AUTH_CACHE_ENABLED: 'true',
  PER_REQUEST_AUTH_CACHE_MAX_AGE: '300',
  PER_REQUEST_AUTH_LOG_VALIDATION: 'false'
};

function showHelp() {
  console.log(colorize('\n🔧 Authentication Configuration Switcher', 'bold'));
  console.log('='.repeat(50));
  console.log('\nUsage:');
  console.log('  node scripts/switch-auth-config.js [option]');
  console.log('\nOptions:');
  console.log(colorize('  --oauth2-enabled', 'green') + '   Enable OAuth2 + all auth checks');
  console.log(colorize('  --oauth2-disabled', 'yellow') + '  Disable OAuth2 + all auth checks');
  console.log(colorize('  --status', 'blue') + '          Show current configuration');
  console.log(colorize('  --help', 'blue') + '            Show this help message');
  console.log('\nExamples:');
  console.log('  # Enable OAuth2 authentication');
  console.log('  npm run config:oauth2-enabled');
  console.log('');
  console.log('  # Disable all authentication');
  console.log('  npm run config:oauth2-disabled');
  console.log('');
  console.log('  # Check current status');
  console.log('  npm run config:status');
}

function updateEnvFile(updates) {
  const envPath = resolve(process.cwd(), '.env');
  
  if (!existsSync(envPath)) {
    console.error(colorize('❌ .env file not found', 'red'));
    process.exit(1);
  }

  let envContent = readFileSync(envPath, 'utf8');
  let updatedCount = 0;

  Object.entries(updates).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
      updatedCount++;
    } else {
      // Add new variable if it doesn't exist
      envContent += `\n${key}=${value}`;
      updatedCount++;
    }
  });

  writeFileSync(envPath, envContent);
  return updatedCount;
}

function getCurrentConfig() {
  const envPath = resolve(process.cwd(), '.env');
  
  if (!existsSync(envPath)) {
    console.error(colorize('❌ .env file not found', 'red'));
    return null;
  }

  const envContent = readFileSync(envPath, 'utf8');
  const config = {};

  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        config[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return config;
}

function showCurrentStatus() {
  console.log(colorize('\n📋 Current Authentication Configuration', 'bold'));
  console.log('='.repeat(50));

  const config = getCurrentConfig();
  if (!config) return;

  const oauth2Enabled = config.OAUTH2_ENABLED === 'true';
  const connectionAuth = config.CONNECTION_AUTH_ENABLED === 'true';
  const perRequestAuth = config.PER_REQUEST_AUTH_ENABLED === 'true';
  const authStrict = config.CONNECTION_AUTH_STRICT === 'true';

  console.log(colorize(`OAuth2: ${oauth2Enabled ? 'ENABLED' : 'DISABLED'}`, oauth2Enabled ? 'green' : 'yellow'));
  console.log(colorize(`Connection Auth: ${connectionAuth ? 'ENABLED' : 'DISABLED'}`, connectionAuth ? 'green' : 'yellow'));
  console.log(colorize(`Per-Request Auth: ${perRequestAuth ? 'ENABLED' : 'DISABLED'}`, perRequestAuth ? 'green' : 'yellow'));
  console.log(colorize(`Auth Strict Mode: ${authStrict ? 'ENABLED' : 'DISABLED'}`, authStrict ? 'green' : 'yellow'));

  console.log(colorize('\n🔧 Configuration Summary:', 'blue'));
  if (oauth2Enabled && connectionAuth && perRequestAuth) {
    console.log(colorize('✅ Full OAuth2 authentication enabled', 'green'));
  } else if (!oauth2Enabled && !connectionAuth && !perRequestAuth) {
    console.log(colorize('🔓 All authentication disabled', 'yellow'));
  } else {
    console.log(colorize('⚠️  Mixed authentication configuration', 'yellow'));
  }

  console.log(colorize(`\n🌐 Server Port: ${config.MCP_SERVER_PORT || '3000'}`, 'blue'));
  console.log(colorize(`🚀 Transport: ${config.TRANSPORT || 'stdio'}`, 'blue'));
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    showHelp();
    return;
  }

  if (args.includes('--status')) {
    showCurrentStatus();
    return;
  }

  if (args.includes('--oauth2-enabled')) {
    console.log(colorize('\n🔐 Enabling OAuth2 Authentication...', 'blue'));
    const updatedCount = updateEnvFile(OAUTH2_ENABLED_CONFIG);
    console.log(colorize(`✅ Updated ${updatedCount} configuration variables`, 'green'));
    console.log(colorize('\n📋 OAuth2 Configuration:', 'blue'));
    console.log('  ✅ OAuth2: ENABLED');
    console.log('  ✅ Connection Auth: ENABLED');
    console.log('  ✅ Per-Request Auth: ENABLED');
    console.log('  ✅ Auth Strict Mode: ENABLED');
    console.log(colorize('\n🚀 Restart the server to apply changes:', 'yellow'));
    console.log('  npm run dev');
    return;
  }

  if (args.includes('--oauth2-disabled')) {
    console.log(colorize('\n🔓 Disabling All Authentication...', 'blue'));
    const updatedCount = updateEnvFile(OAUTH2_DISABLED_CONFIG);
    console.log(colorize(`✅ Updated ${updatedCount} configuration variables`, 'green'));
    console.log(colorize('\n📋 Authentication Configuration:', 'blue'));
    console.log('  🔓 OAuth2: DISABLED');
    console.log('  🔓 Connection Auth: DISABLED');
    console.log('  🔓 Per-Request Auth: DISABLED');
    console.log('  🔓 Auth Strict Mode: DISABLED');
    console.log(colorize('\n🚀 Restart the server to apply changes:', 'yellow'));
    console.log('  npm run dev');
    return;
  }

  console.error(colorize('❌ Unknown option. Use --help for usage information.', 'red'));
  process.exit(1);
}

main();
