#!/usr/bin/env node

/**
 * Check NACOS Configuration Script
 * 
 * This script lists all configurations in NACOS to help debug
 * what dataIds are actually available.
 */

import { NacosConfigClient } from 'nacos';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function checkNacosConfig() {
  console.log(colorize('\n🔍 NACOS Configuration Checker', 'cyan'));
  console.log(colorize('=====================================', 'cyan'));

  // Check environment variables
  const serverAddr = process.env.NACOS_SERVER_ADDR;
  const namespace = process.env.NACOS_NAMESPACE;
  const accessKey = process.env.NACOS_ACCESS_KEY;
  const secretKey = process.env.NACOS_SECRET_KEY;

  console.log(colorize('\n📋 Environment Variables:', 'blue'));
  console.log(`  NACOS_SERVER_ADDR: ${serverAddr ? colorize('SET', 'green') : colorize('NOT SET', 'red')}`);
  console.log(`  NACOS_NAMESPACE: ${namespace ? colorize('SET', 'green') : colorize('NOT SET', 'red')}`);
  console.log(`  NACOS_ACCESS_KEY: ${accessKey ? colorize('SET', 'green') : colorize('NOT SET', 'red')}`);
  console.log(`  NACOS_SECRET_KEY: ${secretKey ? colorize('SET', 'green') : colorize('NOT SET', 'red')}`);

  if (!serverAddr || !namespace || !accessKey || !secretKey) {
    console.log(colorize('\n❌ Missing NACOS environment variables', 'red'));
    console.log(colorize('💡 Set these environment variables to connect to NACOS:', 'yellow'));
    console.log('  export NACOS_SERVER_ADDR=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848');
    console.log('  export NACOS_NAMESPACE=8bc453ff-d8df-4ee3-acf4-4de86c19f955');
    console.log('  export NACOS_ACCESS_KEY=your-access-key');
    console.log('  export NACOS_SECRET_KEY=your-secret-key');
    return;
  }

  try {
    console.log(colorize('\n🔗 Connecting to NACOS...', 'blue'));
    const client = new NacosConfigClient({
      serverAddr,
      namespace,
      accessKey,
      secretKey,
    });

    // Try to get our specific configuration
    const dataId = process.env.NACOS_DATA_ID || 'com.ikea.mpp:mcp-server-odi-dev';
    const group = process.env.NACOS_GROUP || 'DEFAULT_GROUP';

    console.log(colorize(`\n🎯 Checking for specific configuration:`, 'blue'));
    console.log(`  DataId: ${dataId}`);
    console.log(`  Group: ${group}`);

    const config = await client.getConfig(dataId, group);
    
    if (config) {
      console.log(colorize('\n✅ Configuration found!', 'green'));
      console.log(colorize('📄 Content:', 'blue'));
      console.log(config);
    } else {
      console.log(colorize('\n❌ Configuration not found', 'red'));
      console.log(colorize('\n🔍 This could mean:', 'yellow'));
      console.log('  1. Configuration not uploaded to NACOS yet');
      console.log('  2. Wrong dataId or group');
      console.log('  3. Different namespace or environment');
      console.log('  4. Access permissions issue');
    }

    // Try to list configurations (if API supports it)
    console.log(colorize('\n📋 Attempting to list available configurations...', 'blue'));
    console.log(colorize('(Note: This may not be supported by all NACOS versions)', 'yellow'));

  } catch (error) {
    console.log(colorize('\n❌ Error connecting to NACOS:', 'red'));
    console.log(error.message);
    
    if (error.message.includes('401') || error.message.includes('403')) {
      console.log(colorize('\n🔐 Authentication Error:', 'yellow'));
      console.log('  Check your NACOS_ACCESS_KEY and NACOS_SECRET_KEY');
    } else if (error.message.includes('timeout') || error.message.includes('ECONNREFUSED')) {
      console.log(colorize('\n🌐 Connection Error:', 'yellow'));
      console.log('  Check your NACOS_SERVER_ADDR and network connectivity');
    }
  }
}

// Run the checker
checkNacosConfig().catch(error => {
  console.error(colorize('❌ Script failed:', 'red'), error);
  process.exit(1);
});
