#!/usr/bin/env node

/**
 * Test script to verify permission service interaction with real JWT token
 * This will test both immediate token exchange and service-specific clientId
 */

import fetch from 'node-fetch';
import fs from 'fs';

const MCP_SERVER_URL = 'http://localhost:6300/mcp';
const SESSION_ID = 'test-session-' + Math.random().toString(36).substring(2, 15);

// Load the real JWT token
const tokenData = JSON.parse(fs.readFileSync('./local-only/token.ingka-dt.cn.json', 'utf8'));
const JWT_TOKEN = tokenData.access_token;

async function testPermissionServiceInteraction() {
  console.log('🧪 Testing Permission Service Interaction with Real JWT Token');
  console.log('='.repeat(70));
  console.log(`📡 Server: ${MCP_SERVER_URL}`);
  console.log(`🔑 Session: ${SESSION_ID}`);
  console.log(`🎫 JWT Token: ${JWT_TOKEN.substring(0, 50)}...`);
  console.log('');

  try {
    // Step 1: Initialize MCP session with JWT token
    console.log('📡 Step 1: MCP Initialize with JWT Token');
    console.log('   This should trigger immediate McpUserToken exchange upon JWT validation');

    const initResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
        Authorization: `Bearer ${JWT_TOKEN}`,
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: { tools: {} },
          clientInfo: { name: 'permission-test-client', version: '1.0.0' },
        },
      }),
    });

    const initResult = await initResponse.json();
    console.log(`✅ Initialize: ${initResponse.status}`);

    if (initResult.error) {
      console.log(`❌ Error: ${initResult.error.message}`);
      console.log(`   Code: ${initResult.error.code}`);
      return;
    }

    if (initResult.result) {
      console.log(`   Protocol: ${initResult.result.protocolVersion}`);
      console.log(
        `   Server: ${initResult.result.serverInfo?.name} v${initResult.result.serverInfo?.version}`
      );
    }
    console.log('');

    // Step 2: Test OMS service call (should use service-specific clientId: orders-portal)
    console.log('🏪 Step 2: Test OMS Service Call');
    console.log('   This should use service-specific clientId: orders-portal');
    console.log('   Look for "Using service-specific clientId for token exchange" in server logs');

    const omsResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
        Authorization: `Bearer ${JWT_TOKEN}`,
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: 'oms_queryOrderLists',
          arguments: { page: 1, pageSize: 5 },
        },
      }),
    });

    const omsResult = await omsResponse.json();
    console.log(`📊 OMS Query Result: ${omsResponse.status}`);

    if (omsResult.error) {
      console.log(`❌ Error: ${omsResult.error.message}`);
      console.log(`   Code: ${omsResult.error.code}`);
      if (omsResult.error.data) {
        console.log(`   Details:`, JSON.stringify(omsResult.error.data, null, 2));
      }
    } else {
      console.log(`✅ Success: OMS service call completed`);
      const content = omsResult.result?.content?.[0]?.text;
      if (content) {
        try {
          const parsed = JSON.parse(content);
          console.log(`   Success: ${parsed.success}`);
          console.log(`   Module: ${parsed.meta?.module}`);
          console.log(`   Function: ${parsed.meta?.function}`);
        } catch (e) {
          console.log(`   Raw response: ${content.substring(0, 200)}...`);
        }
      }
    }

    console.log('');
    console.log('🔍 Expected Server Logs:');
    console.log('   1. "JWT token validated - obtaining McpUserToken immediately..."');
    console.log('   2. "McpUserToken obtained and stored in authInfo"');
    console.log('   3. "Using service-specific clientId for token exchange"');
    console.log('   4. "serviceSpecificClientId: orders-portal"');
    console.log('   5. "Permission service HTTP request" with detailed headers');
    console.log('   6. "Permission service response" with parsing details');
    console.log('   7. "Successfully obtained from permission service"');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.cause) {
      console.error('   Cause:', error.cause);
    }
  }
}

// Run the test
testPermissionServiceInteraction().catch(console.error);
