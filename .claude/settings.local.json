{"permissions": {"allow": ["Bash(node:*)", "Bash(MCP_SERVER_URL=http://localhost:6300 node blackbox/tests/oauth-endpoints-test.js)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(gh workflow run:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(argocd app list:*)", "Bash(argocd app get:*)", "Bash(grep:*)", "Bash(ln:*)", "Bash(gh run view:*)", "<PERSON><PERSON>(tsc:*)", "Bash(npm run build)", "Bash(gh workflow list:*)", "<PERSON><PERSON>(cat:*)", "Bash(git log:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/Work"]}}