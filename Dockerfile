# 🐳 Multi-stage Dockerfile for MCP Server ODI
# Optimized for production deployment with security best practices

# ==================== BUILD STAGE ====================
FROM artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install production dependencies and essential build tools only
# Set DOCKER_BUILD to skip husky installation
ENV DOCKER_BUILD=true
RUN npm ci --only=production && \
    npm install --no-save typescript@^5.0.0 shx@^0.3.4 @types/node@^20.0.0 @types/express@^5.0.1

# Copy source code
COPY src/ ./src/

# Build the application
RUN npm run build

# Verify build output
RUN ls -la dist/ && test -f dist/index.js

# Install clean production dependencies for runtime
RUN rm -rf node_modules && npm ci --only=production

# ==================== PRODUCTION STAGE ====================
FROM artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/node:20-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S mcpuser && \
    adduser -S mcpuser -u 1001 -G mcpuser

# Set working directory
WORKDIR /app

# Copy package files for reference
COPY package*.json ./

# Copy built application and clean production node_modules from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

# Copy any additional runtime files if needed
# COPY docs/ ./docs/
# COPY scripts/ ./scripts/

# Create logs directory with proper permissions
RUN mkdir -p logs && \
    chown -R mcpuser:mcpuser /app

# Switch to non-root user
USER mcpuser

# Environment variables are provided by Helm (K8s) and NACOS
# No defaults set here to avoid configuration conflicts and ensure explicit configuration

# Health check (port 8080 is standard for this service)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "fetch('http://localhost:8080/health').then(r=>r.ok?process.exit(0):process.exit(1)).catch(()=>process.exit(1))"

# Expose port 8080 (standard port for this service)
EXPOSE 8080

# Labels for metadata
LABEL maintainer="IKEA CN ODI Team" \
      version="0.1.0" \
      description="MCP Server ODI" \
      org.opencontainers.image.title="MCP Server ODI" \
      org.opencontainers.image.description="Model Context Protocol server for IKEA ODI" \
      org.opencontainers.image.version="0.1.0" \
      org.opencontainers.image.vendor="IKEA" \
      org.opencontainers.image.licenses="MIT"

# Start the application
CMD ["node", "dist/index.js"]
