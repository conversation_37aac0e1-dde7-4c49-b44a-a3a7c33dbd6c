# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Run
```bash
npm run build      # Compile TypeScript to JavaScript
npm start          # Run production build
npm run dev        # Development mode with auto-reload
npm run watch      # Watch mode for TypeScript compilation
npm run inspect    # Start with MC<PERSON> Inspector for interactive testing
```

### Testing
```bash
npm test                   # Run unit tests and type checking
npm run test:type          # Type checking only
npm run test:unit          # Unit tests only
npm run test:coverage      # Unit tests with coverage
npm run test:integration   # Integration tests (requires Docker)
npm run test:blackbox      # Blackbox tests (quick)
npm run test:blackbox:full # Complete blackbox test suite
npm run test:oauth         # OAuth2 tests
npm run test:oauth:quick   # Quick OAuth2 tests
```

### Linting & Formatting
```bash
npm run lint          # ESLint check
npm run lint:fix      # ESLint check and fix
npm run format        # Prettier formatting
npm run format:check  # Prettier check
```

### Configuration & Validation
```bash
npm run validate:env          # Validate environment configuration
npm run config:oauth2-enabled # Enable OAuth2 authentication
npm run config:oauth2-disabled # Disable OAuth2 authentication
npm run config:status         # Check current auth configuration
npm run nacos:validate        # Validate NACOS configuration
npm run nacos:test           # Test NACOS connection
```

## Architecture Overview

This is a **Model Context Protocol (MCP) Server** providing access to ODI (Order Data Integration) services through standardized MCP tools.

### Core Components
- **src/server.ts**: Main MCP server setup with tool/resource/prompt registration
- **src/adapters/service-adapter.ts**: Service implementations and HTTP request handling
- **src/transport/http-transport.ts**: HTTP transport layer with OAuth2 support
- **src/auth/**: Authentication middleware, OAuth2 providers, and token management
- **src/config/**: Configuration management with NACOS integration

### Service Modules
- **oms** (Order Management System): Order query, logistics, invoice management tools
- **global**: Global user and system tools
- **test**: Testing and debugging tools

### Authentication
- **Cookie-based auth**: Traditional session cookies from ODI Web
- **OAuth2**: Keycloak-based authentication with MCP compliance
- **Dual-mode**: Supports both authentication methods with config switching

### Key Dependencies
- `@modelcontextprotocol/sdk`: MCP protocol implementation
- `express`: HTTP server framework
- `zod`: Schema validation
- `nacos`: Configuration management
- `jwks-rsa`: JWT validation for OAuth2

### Testing Strategy
- **Unit tests**: Jest tests in `tests/` directory
- **Integration tests**: Docker-based tests in `blackbox/tests/`
- **Blackbox tests**: Comprehensive service testing suite
- **OAuth2 tests**: Authentication flow validation

### Configuration Hierarchy
1. **NACOS**: Centralized configuration (highest priority)
2. **Environment variables**: Process-level configuration
3. **Default values**: Fallback configuration

Use `npm run validate:env` to check configuration and `npm run nacos:test` to verify NACOS connectivity.