import dotenv from 'dotenv';
import path from 'path';
import nacosConfigManager from '../config/nacos.js';

// Load .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

/**
 * 环境配置工具
 * 从环境变量中读取配置信息
 */

/**
 * Helper function to convert string values to appropriate types
 * Exported for testing purposes
 */
export function convertConfigTypes(config: any): any {
  const converted = { ...config };

  // Convert boolean strings to actual booleans
  const booleanFields = [
    'DEBUG_SERVICE_ADAPTER',
    'OAUTH2_ENABLED',
    'CONNECTION_AUTH_ENABLED',
    'CONNECTION_AUTH_STRICT',
    'CONNECTION_AUTH_TEST_API',
    'PER_REQUEST_AUTH_ENABLED',
    'PER_REQUEST_AUTH_CACHE_ENABLED',
    'PER_REQUEST_AUTH_LOG_VALIDATION',
  ];

  booleanFields.forEach(field => {
    if (typeof converted[field] === 'string') {
      converted[field] = converted[field] === 'true';
    } else if (converted[field] === undefined) {
      converted[field] = false; // Default to false for boolean fields
    }
  });

  // Note: OAUTH2_SCOPES removed - now provided by clients in requests

  // Convert number strings to numbers
  if (typeof converted.MCP_SERVER_PORT === 'string') {
    converted.MCP_SERVER_PORT = parseInt(converted.MCP_SERVER_PORT, 10);
  }
  if (typeof converted.PER_REQUEST_AUTH_CACHE_MAX_AGE === 'string') {
    converted.PER_REQUEST_AUTH_CACHE_MAX_AGE = parseInt(
      converted.PER_REQUEST_AUTH_CACHE_MAX_AGE,
      10
    );
  }

  return converted;
}

export interface EnvironmentConfig {
  NODE_ENV: string;
  VITE_API_HOST: string;
  VITE_API_HOST_KONG: string;
  VITE_MASTER_DATA_API_HOST: string;
  VITE_MASTER_DATA_API_KEY: string;
  AUTH_COOKIES: string;
  X_CUSTOM_REFERRER: string;
  DEBUG_SERVICE_ADAPTER: boolean;
  // Transport Configuration
  TRANSPORT: 'stdio' | 'http';
  // Unified Port Configuration
  MCP_SERVER_PORT: number;
  MCP_SERVER_BASE_URL: string;
  MCP_SERVER_PUBLIC_BASE_URL: string;
  // OAuth2 Configuration (Server-Side Only)
  OAUTH2_ENABLED: boolean;
  KEYCLOAK_BASE_URL: string;
  KEYCLOAK_REALM: string;
  OAUTH2_CLIENT_SECRET: string;
  OAUTH2_CLIENT_AUTH_METHOD: 'client_secret_basic' | 'none';
  OAUTH2_ISSUER_URL: string;
  // Note: client_id, redirect_uri, and scopes are provided by clients in requests
  // Connection Authentication
  CONNECTION_AUTH_ENABLED: boolean;
  CONNECTION_AUTH_STRICT: boolean;
  CONNECTION_AUTH_TEST_API: boolean;
  // Per-Request Authentication
  PER_REQUEST_AUTH_ENABLED: boolean;
  PER_REQUEST_AUTH_CACHE_ENABLED: boolean;
  PER_REQUEST_AUTH_CACHE_MAX_AGE: number;
  PER_REQUEST_AUTH_LOG_VALIDATION: boolean;
  // Service Endpoints (all use MCP_SERVER_PORT)
  MCP_ENDPOINT_URL: string;
  OAUTH2_METADATA_URL: string;
  OAUTH2_AUTHORIZATION_URL: string;
  OAUTH2_TOKEN_URL: string;
  OAUTH2_REVOCATION_URL: string;
  HEALTH_ENDPOINT_URL: string;
}

/**
 * 获取环境配置 (Synchronous fallback without NACOS)
 *
 * This function provides a synchronous fallback for cases where async is not possible.
 * It only uses environment variables and defaults, without NACOS integration.
 * Use this only when the async getEnvironmentConfig() cannot be used.
 */
export function getEnvironmentConfigSync(): EnvironmentConfig {
  const keycloakBaseUrl = process.env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn';
  const keycloakRealm = process.env.KEYCLOAK_REALM || 'master';
  const mcpServerPort = parseInt(process.env.MCP_SERVER_PORT || '8080', 10);
  const mcpServerBaseUrl = process.env.MCP_SERVER_BASE_URL || `http://localhost:${mcpServerPort}`;
  const mcpServerPublicBaseUrl = process.env.MCP_SERVER_PUBLIC_BASE_URL || mcpServerBaseUrl;

  return {
    // Infrastructure Configuration
    NODE_ENV: process.env.NODE_ENV || 'development',
    TRANSPORT: (process.env.TRANSPORT === 'http' ? 'http' : 'stdio') as 'stdio' | 'http',
    MCP_SERVER_PORT: mcpServerPort,
    MCP_SERVER_BASE_URL: mcpServerBaseUrl,
    MCP_SERVER_PUBLIC_BASE_URL: mcpServerPublicBaseUrl,

    // API Configuration (Environment Variables + Safe Defaults)
    VITE_API_HOST: process.env.VITE_API_HOST || 'https://api-dev-i.ingka-dt.cn',
    VITE_API_HOST_KONG: process.env.VITE_API_HOST_KONG || 'https://api-dev-i.ingka-dt.cn',
    VITE_MASTER_DATA_API_HOST:
      process.env.VITE_MASTER_DATA_API_HOST || 'https://master-data-api-dev.ingka-dt.cn',
    VITE_MASTER_DATA_API_KEY: process.env.VITE_MASTER_DATA_API_KEY || '',

    // Authentication Configuration
    AUTH_COOKIES: process.env.AUTH_COOKIES || '',
    X_CUSTOM_REFERRER: process.env.X_CUSTOM_REFERRER || 'https://fe-dev-i.ingka-dt.cn/order-web',

    // Feature Flags (Safe Defaults)
    DEBUG_SERVICE_ADAPTER: process.env.DEBUG_SERVICE_ADAPTER === 'true',

    // OAuth2 Configuration (Server-Side Only)
    OAUTH2_ENABLED: process.env.OAUTH2_ENABLED === 'true',
    KEYCLOAK_BASE_URL: keycloakBaseUrl,
    KEYCLOAK_REALM: keycloakRealm,
    OAUTH2_CLIENT_SECRET: process.env.OAUTH2_CLIENT_SECRET || '',
    OAUTH2_CLIENT_AUTH_METHOD:
      (process.env.OAUTH2_CLIENT_AUTH_METHOD as 'client_secret_basic' | 'none') || 'none', // Default to 'none' for public clients
    OAUTH2_ISSUER_URL: `${keycloakBaseUrl}/realms/${keycloakRealm}`,
    // Connection Authentication
    CONNECTION_AUTH_ENABLED: process.env.CONNECTION_AUTH_ENABLED === 'true',
    CONNECTION_AUTH_STRICT: process.env.CONNECTION_AUTH_STRICT === 'true',
    CONNECTION_AUTH_TEST_API: process.env.CONNECTION_AUTH_TEST_API === 'true',
    // Per-Request Authentication
    PER_REQUEST_AUTH_ENABLED: process.env.PER_REQUEST_AUTH_ENABLED === 'true',
    PER_REQUEST_AUTH_CACHE_ENABLED: process.env.PER_REQUEST_AUTH_CACHE_ENABLED !== 'false',
    PER_REQUEST_AUTH_CACHE_MAX_AGE: parseInt(process.env.PER_REQUEST_AUTH_CACHE_MAX_AGE || '300'),
    PER_REQUEST_AUTH_LOG_VALIDATION: process.env.PER_REQUEST_AUTH_LOG_VALIDATION === 'true',
    // Service Endpoints (all use MCP_SERVER_PORT)
    MCP_ENDPOINT_URL: `${mcpServerBaseUrl}/mcp`,
    OAUTH2_METADATA_URL: `${mcpServerPublicBaseUrl}/.well-known/oauth-protected-resource`,
    OAUTH2_AUTHORIZATION_URL: `${mcpServerPublicBaseUrl}/authorize`,
    OAUTH2_TOKEN_URL: `${mcpServerPublicBaseUrl}/token`,
    OAUTH2_REVOCATION_URL: `${mcpServerPublicBaseUrl}/revoke`,
    HEALTH_ENDPOINT_URL: `${mcpServerBaseUrl}/health`,
  };
}

/**
 * 获取环境配置 (Primary configuration function with NACOS integration)
 *
 * This is the main configuration function that handles NACOS availability gracefully.
 * If NACOS is unavailable, falls back to environment variables and defaults.
 *
 * Configuration Precedence (highest to lowest):
 * 1. NACOS Configuration (production control)
 * 2. Environment Variables (K8s/Docker)
 * 3. Application Defaults (safe fallbacks)
 */
export async function getEnvironmentConfig(): Promise<EnvironmentConfig> {
  // Load configuration from NACOS first
  const nacosConfig = await nacosConfigManager.loadConfig();

  // Infrastructure variables (always from environment, never from NACOS)
  const mcpServerPort = parseInt(process.env.MCP_SERVER_PORT || '8080', 10);
  const mcpServerBaseUrl = process.env.MCP_SERVER_BASE_URL || `http://localhost:${mcpServerPort}`;
  const mcpServerPublicBaseUrl = process.env.MCP_SERVER_PUBLIC_BASE_URL || mcpServerBaseUrl;

  // Default configuration
  const defaultVars = {
    // Infrastructure Configuration
    NODE_ENV: 'development',
    TRANSPORT: 'http' as 'stdio' | 'http',
    MCP_SERVER_PORT: 8080,
    MCP_SERVER_BASE_URL: 'http://localhost:8080',
    MCP_SERVER_PUBLIC_BASE_URL: 'http://localhost:8080',

    // API Configuration
    VITE_API_HOST: 'https://api-dev-i.ingka-dt.cn',
    VITE_API_HOST_KONG: 'https://api-dev-i.ingka-dt.cn',
    VITE_MASTER_DATA_API_HOST: 'https://master-data-api-dev.ingka-dt.cn',
    VITE_MASTER_DATA_API_KEY: '',

    // Authentication Configuration
    AUTH_COOKIES: '',
    X_CUSTOM_REFERRER: 'https://fe-dev-i.ingka-dt.cn/order-web',

    // Feature Flags
    DEBUG_SERVICE_ADAPTER: false,
    OAUTH2_ENABLED: false,

    // OAuth2 Configuration
    KEYCLOAK_BASE_URL: 'https://keycloak.ingka-dt.cn',
    KEYCLOAK_REALM: 'master',
    // Note: Client-side parameters removed - provided by clients in requests
    OAUTH2_CLIENT_SECRET: '',
    OAUTH2_CLIENT_AUTH_METHOD: 'client_secret_basic' as 'client_secret_basic' | 'none',
    OAUTH2_ISSUER_URL: 'https://keycloak.ingka-dt.cn/realms/master',

    // Connection Authentication
    CONNECTION_AUTH_ENABLED: false,
    CONNECTION_AUTH_STRICT: false,
    CONNECTION_AUTH_TEST_API: false,
  };

  // Environment variables (filter out undefined values)
  const envVars = Object.fromEntries(
    Object.entries({
      NODE_ENV: process.env.NODE_ENV,
      TRANSPORT: process.env.TRANSPORT,
      MCP_SERVER_PORT: process.env.MCP_SERVER_PORT,
      MCP_SERVER_BASE_URL: process.env.MCP_SERVER_BASE_URL,
      VITE_API_HOST: process.env.VITE_API_HOST,
      VITE_API_HOST_KONG: process.env.VITE_API_HOST_KONG,
      VITE_MASTER_DATA_API_HOST: process.env.VITE_MASTER_DATA_API_HOST,
      VITE_MASTER_DATA_API_KEY: process.env.VITE_MASTER_DATA_API_KEY,
      AUTH_COOKIES: process.env.AUTH_COOKIES,
      X_CUSTOM_REFERRER: process.env.X_CUSTOM_REFERRER,
      DEBUG_SERVICE_ADAPTER: process.env.DEBUG_SERVICE_ADAPTER,
      OAUTH2_ENABLED: process.env.OAUTH2_ENABLED,
      KEYCLOAK_BASE_URL: process.env.KEYCLOAK_BASE_URL,
      KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
      // Note: Client-side parameters removed - provided by clients in requests
      OAUTH2_CLIENT_SECRET: process.env.OAUTH2_CLIENT_SECRET,
      OAUTH2_CLIENT_AUTH_METHOD: process.env.OAUTH2_CLIENT_AUTH_METHOD,
      CONNECTION_AUTH_ENABLED: process.env.CONNECTION_AUTH_ENABLED,
      CONNECTION_AUTH_STRICT: process.env.CONNECTION_AUTH_STRICT,
      CONNECTION_AUTH_TEST_API: process.env.CONNECTION_AUTH_TEST_API,
      // Per-Request Authentication variables (missing!)
      PER_REQUEST_AUTH_ENABLED: process.env.PER_REQUEST_AUTH_ENABLED,
      PER_REQUEST_AUTH_CACHE_ENABLED: process.env.PER_REQUEST_AUTH_CACHE_ENABLED,
      PER_REQUEST_AUTH_CACHE_MAX_AGE: process.env.PER_REQUEST_AUTH_CACHE_MAX_AGE,
      PER_REQUEST_AUTH_LOG_VALIDATION: process.env.PER_REQUEST_AUTH_LOG_VALIDATION,
    }).filter(([_, value]) => value !== undefined)
  );

  // NACOS variables (filter out undefined values)
  const nacosVars = Object.fromEntries(
    Object.entries(nacosConfig).filter(([_, value]) => value !== undefined)
  );

  // Debug: Log the merge process for OAUTH2_ENABLED
  console.error('🔍 [Config] OAUTH2_ENABLED merge debug:');
  console.error(`   Default: ${defaultVars.OAUTH2_ENABLED}`);
  console.error(`   Environment: ${envVars.OAUTH2_ENABLED}`);
  console.error(`   NACOS: ${nacosVars.OAUTH2_ENABLED}`);

  // Merge with clear precedence: defaults < env < nacos
  const mergedConfig = Object.assign({}, defaultVars, envVars, nacosVars);

  console.error(`   Merged: ${mergedConfig.OAUTH2_ENABLED}`);

  // Apply type conversions
  const typedConfig = convertConfigTypes(mergedConfig);

  console.error(`   After type conversion: ${typedConfig.OAUTH2_ENABLED}`);

  // Apply infrastructure overrides (always from environment)
  typedConfig.MCP_SERVER_PORT = mcpServerPort;
  typedConfig.MCP_SERVER_BASE_URL = mcpServerBaseUrl;
  typedConfig.MCP_SERVER_PUBLIC_BASE_URL = mcpServerPublicBaseUrl;

  // Calculate computed values (server-side only)
  const calculatedVars = {
    // Note: OAUTH2_REDIRECT_URI removed - provided by clients in requests
    OAUTH2_ISSUER_URL: `${typedConfig.KEYCLOAK_BASE_URL}/realms/${typedConfig.KEYCLOAK_REALM}`,
    MCP_ENDPOINT_URL: `${typedConfig.MCP_SERVER_BASE_URL}/mcp`,
    OAUTH2_METADATA_URL: `${typedConfig.MCP_SERVER_PUBLIC_BASE_URL}/.well-known/oauth-protected-resource`,
    OAUTH2_AUTHORIZATION_URL: `${typedConfig.MCP_SERVER_PUBLIC_BASE_URL}/authorize`,
    OAUTH2_TOKEN_URL: `${typedConfig.MCP_SERVER_PUBLIC_BASE_URL}/token`,
    OAUTH2_REVOCATION_URL: `${typedConfig.MCP_SERVER_PUBLIC_BASE_URL}/revoke`,
    HEALTH_ENDPOINT_URL: `${typedConfig.MCP_SERVER_BASE_URL}/health`,
  };

  // Final merge with calculated values
  const finalConfig = Object.assign({}, typedConfig, calculatedVars);

  return finalConfig as EnvironmentConfig;
}

/**
 * 验证必需的环境变量 (with NACOS integration)
 */
export async function validateEnvironment(): Promise<{
  valid: boolean;
  missing: string[];
  warnings: string[];
  security: string[];
}> {
  const config = await getEnvironmentConfig();
  const missing: string[] = [];
  const warnings: string[] = [];
  const security: string[] = [];

  // 检查基础配置
  if (!config.AUTH_COOKIES && !config.OAUTH2_ENABLED) {
    missing.push('AUTH_COOKIES (required when OAuth2 is disabled)');
  }

  if (!config.VITE_API_HOST_KONG) {
    missing.push('VITE_API_HOST_KONG');
  }

  // 检查OAuth2配置（如果启用）
  if (config.OAUTH2_ENABLED) {
    // Note: OAUTH2_CLIENT_ID is now provided by clients in requests, not server config

    // OAUTH2_CLIENT_SECRET is required for client_secret_basic, optional for none
    if (
      config.OAUTH2_CLIENT_AUTH_METHOD === 'client_secret_basic' &&
      !config.OAUTH2_CLIENT_SECRET
    ) {
      missing.push('OAUTH2_CLIENT_SECRET (required for client_secret_basic authentication)');
    }
    if (!config.KEYCLOAK_BASE_URL) {
      missing.push('KEYCLOAK_BASE_URL');
    }
    if (!config.KEYCLOAK_REALM) {
      missing.push('KEYCLOAK_REALM');
    }
  }

  // 安全检查
  if (config.NODE_ENV === 'production') {
    if (config.DEBUG_SERVICE_ADAPTER) {
      security.push('DEBUG_SERVICE_ADAPTER should be false in production');
    }

    if (config.TRANSPORT === 'stdio' && config.MCP_SERVER_PORT !== 3000) {
      warnings.push('MCP_SERVER_PORT is ignored in stdio transport mode');
    }
  }

  // 端口验证
  if (config.MCP_SERVER_PORT < 1024 || config.MCP_SERVER_PORT > 65535) {
    warnings.push('MCP_SERVER_PORT should be between 1024 and 65535');
  }

  // URL 格式验证
  try {
    new URL(config.VITE_API_HOST);
  } catch {
    missing.push('VITE_API_HOST must be a valid URL');
  }

  try {
    new URL(config.VITE_API_HOST_KONG);
  } catch {
    missing.push('VITE_API_HOST_KONG must be a valid URL');
  }

  // OAuth2 URL 验证
  if (config.OAUTH2_ENABLED) {
    try {
      new URL(config.KEYCLOAK_BASE_URL);
    } catch {
      missing.push('KEYCLOAK_BASE_URL must be a valid URL');
    }
  }

  return {
    valid: missing.length === 0,
    missing,
    warnings,
    security,
  };
}

/**
 * 验证环境变量（包含NACOS配置）
 * Enhanced version that validates NACOS-integrated configuration
 */
export function validateEnvironmentWithNacos(config: EnvironmentConfig): {
  valid: boolean;
  missing: string[];
  warnings: string[];
  security: string[];
} {
  const missing: string[] = [];
  const warnings: string[] = [];
  const security: string[] = [];

  // 检查基础配置
  if (!config.AUTH_COOKIES && !config.OAUTH2_ENABLED) {
    missing.push('AUTH_COOKIES (required when OAuth2 is disabled)');
  }

  if (!config.VITE_API_HOST_KONG) {
    missing.push('VITE_API_HOST_KONG');
  }

  // 检查OAuth2配置（如果启用）
  if (config.OAUTH2_ENABLED) {
    // Note: OAUTH2_CLIENT_ID is now provided by clients in requests, not server config
    // OAUTH2_CLIENT_SECRET is required for client_secret_basic, optional for none
    if (
      config.OAUTH2_CLIENT_AUTH_METHOD === 'client_secret_basic' &&
      !config.OAUTH2_CLIENT_SECRET
    ) {
      missing.push('OAUTH2_CLIENT_SECRET (required for client_secret_basic authentication)');
    }
    if (!config.KEYCLOAK_BASE_URL) {
      missing.push('KEYCLOAK_BASE_URL');
    }
    if (!config.KEYCLOAK_REALM) {
      missing.push('KEYCLOAK_REALM');
    }
  }

  // 安全检查
  if (config.NODE_ENV === 'production') {
    if (config.DEBUG_SERVICE_ADAPTER) {
      security.push('DEBUG_SERVICE_ADAPTER should be false in production');
    }

    if (config.TRANSPORT === 'stdio' && config.MCP_SERVER_PORT !== 3000) {
      warnings.push('MCP_SERVER_PORT is ignored in stdio transport mode');
    }
  }

  // 端口验证
  if (config.MCP_SERVER_PORT < 1024 || config.MCP_SERVER_PORT > 65535) {
    warnings.push('MCP_SERVER_PORT should be between 1024 and 65535');
  }

  // URL 格式验证
  try {
    new URL(config.VITE_API_HOST);
  } catch {
    missing.push('VITE_API_HOST must be a valid URL');
  }

  try {
    new URL(config.VITE_API_HOST_KONG);
  } catch {
    missing.push('VITE_API_HOST_KONG must be a valid URL');
  }

  // OAuth2 URL 验证
  if (config.OAUTH2_ENABLED) {
    try {
      new URL(config.KEYCLOAK_BASE_URL);
    } catch {
      missing.push('KEYCLOAK_BASE_URL must be a valid URL');
    }
  }

  return {
    valid: missing.length === 0,
    missing,
    warnings,
    security,
  };
}

/**
 * 打印环境配置信息（隐藏敏感信息）
 * Uses NACOS-integrated configuration
 */
export async function logEnvironmentInfo() {
  const config = await getEnvironmentConfig();

  console.error('🌍 Environment Configuration (with NACOS integration):');
  console.error(`  NODE_ENV: ${config.NODE_ENV}`);
  console.error(`  TRANSPORT: ${config.TRANSPORT}`);
  console.error(`  VITE_API_HOST: ${config.VITE_API_HOST}`);
  console.error(`  VITE_API_HOST_KONG: ${config.VITE_API_HOST_KONG}`);
  console.error(`  VITE_MASTER_DATA_API_HOST: ${config.VITE_MASTER_DATA_API_HOST}`);
  console.error(
    `  VITE_MASTER_DATA_API_KEY: ${config.VITE_MASTER_DATA_API_KEY ? '***SET***' : '***NOT SET***'}`
  );
  console.error(`  AUTH_COOKIES: ${config.AUTH_COOKIES ? '***SET***' : '***NOT SET***'}`);

  console.error('🚀 MCP Server Configuration:');
  console.error(`  PORT: ${config.MCP_SERVER_PORT}`);
  console.error(`  BASE_URL: ${config.MCP_SERVER_BASE_URL}`);
  console.error(`  MCP_ENDPOINT: ${config.MCP_ENDPOINT_URL}`);
  console.error(`  HEALTH_ENDPOINT: ${config.HEALTH_ENDPOINT_URL}`);

  console.error('🔐 OAuth2 Configuration:');
  console.error(`  OAUTH2_ENABLED: ${config.OAUTH2_ENABLED}`);
  if (config.OAUTH2_ENABLED) {
    console.error(`  KEYCLOAK_BASE_URL: ${config.KEYCLOAK_BASE_URL}`);
    console.error(`  KEYCLOAK_REALM: ${config.KEYCLOAK_REALM}`);
    console.error(
      `  OAUTH2_CLIENT_SECRET: ${config.OAUTH2_CLIENT_SECRET ? '***SET***' : '***NOT SET***'}`
    );
    console.error(`  OAUTH2_CLIENT_AUTH_METHOD: ${config.OAUTH2_CLIENT_AUTH_METHOD}`);
    console.error(`  OAUTH2_ISSUER_URL: ${config.OAUTH2_ISSUER_URL}`);
    console.error(`  Note: client_id, redirect_uri, and scopes provided by clients in requests`);

    console.error('🔗 OAuth2 Service Endpoints:');
    console.error(`  METADATA: ${config.OAUTH2_METADATA_URL}`);
    console.error(`  AUTHORIZATION: ${config.OAUTH2_AUTHORIZATION_URL}`);
    console.error(`  TOKEN: ${config.OAUTH2_TOKEN_URL}`);
    console.error(`  REVOCATION: ${config.OAUTH2_REVOCATION_URL}`);
    console.error(`  MCP_SERVER_BASE_URL: ${config.MCP_SERVER_BASE_URL}`);
    console.error(`  MCP_SERVER_PORT: ${config.MCP_SERVER_PORT}`);
  }
}

/**
 * 打印环境配置信息（包含NACOS配置）
 * Enhanced version - uses NACOS-integrated configuration
 */
export function logEnvironmentInfoWithNacos(config: EnvironmentConfig) {
  console.error('🌍 Environment Configuration (with NACOS):');
  console.error(`  NODE_ENV: ${config.NODE_ENV}`);
  console.error(`  TRANSPORT: ${config.TRANSPORT}`);
  console.error(`  VITE_API_HOST: ${config.VITE_API_HOST}`);
  console.error(`  VITE_API_HOST_KONG: ${config.VITE_API_HOST_KONG}`);
  console.error(`  VITE_MASTER_DATA_API_HOST: ${config.VITE_MASTER_DATA_API_HOST}`);
  console.error(
    `  VITE_MASTER_DATA_API_KEY: ${config.VITE_MASTER_DATA_API_KEY ? '***SET***' : '***NOT SET***'}`
  );
  console.error(`  AUTH_COOKIES: ${config.AUTH_COOKIES ? '***SET***' : '***NOT SET***'}`);

  console.error('🚀 MCP Server Configuration:');
  console.error(`  PORT: ${config.MCP_SERVER_PORT}`);
  console.error(`  BASE_URL: ${config.MCP_SERVER_BASE_URL}`);
  console.error(`  MCP_ENDPOINT: ${config.MCP_ENDPOINT_URL}`);
  console.error(`  HEALTH_ENDPOINT: ${config.HEALTH_ENDPOINT_URL}`);

  console.error('🔐 OAuth2 Configuration:');
  console.error(`  OAUTH2_ENABLED: ${config.OAUTH2_ENABLED}`);

  if (config.OAUTH2_ENABLED) {
    console.error(`  KEYCLOAK_BASE_URL: ${config.KEYCLOAK_BASE_URL}`);
    console.error(`  KEYCLOAK_REALM: ${config.KEYCLOAK_REALM}`);
    console.error(
      `  OAUTH2_CLIENT_SECRET: ${config.OAUTH2_CLIENT_SECRET ? '***SET***' : '***NOT SET***'}`
    );
    console.error(`  OAUTH2_CLIENT_AUTH_METHOD: ${config.OAUTH2_CLIENT_AUTH_METHOD}`);
    console.error(`  Note: client_id, redirect_uri, and scopes provided by clients in requests`);

    console.error('🔗 OAuth2 Service Endpoints:');
    console.error(`  METADATA: ${config.OAUTH2_METADATA_URL}`);
    console.error(`  AUTHORIZATION: ${config.OAUTH2_AUTHORIZATION_URL}`);
    console.error(`  TOKEN: ${config.OAUTH2_TOKEN_URL}`);
    console.error(`  REVOCATION: ${config.OAUTH2_REVOCATION_URL}`);
  }
}
