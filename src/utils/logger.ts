/**
 * 统一日志工具 - MCP Server ODI
 * 提供结构化日志记录，支持不同日志级别和环境配置
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

export interface LogContext {
  module?: string;
  function?: string;
  requestId?: string;
  sessionId?: string;
  userId?: string;
  duration?: number;
  [key: string]: unknown;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  service: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

class Logger {
  private serviceName = 'mcp-server-odi';

  private getCurrentLevel(): LogLevel {
    // 每次都从环境变量读取，支持运行时更改
    const envLevel = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    return LogLevel[envLevel as keyof typeof LogLevel] ?? LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.getCurrentLevel();
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext, error?: Error): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      service: this.serviceName,
    };

    if (context) {
      entry.context = { ...context };
    }

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      };
    }

    return entry;
  }

  private output(entry: LogEntry): void {
    // MCP服务器使用stderr输出日志（不干扰stdio协议）
    // 对于remote MCP server，所有结构化日志都输出到stderr
    console.error(JSON.stringify(entry));
  }

  // 专门用于服务器状态的直接输出（保持原有格式，便于运维查看）
  serverStatus(message: string, context?: LogContext): void {
    // 服务器状态信息直接输出到stderr，保持可读格式
    console.error(message);
    
    // 同时记录结构化日志
    if (this.shouldLog(LogLevel.INFO)) {
      this.info(`Server Status: ${message}`, { ...context, serverStatus: true });
    }
  }

  // 专门用于致命错误的直接输出
  fatal(message: string, error?: Error, context?: LogContext): void {
    // 致命错误直接输出到stderr，保持可读格式
    console.error(`❌ FATAL: ${message}`);
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ Stack: ${error.stack}`);
      }
    }
    
    // 同时记录结构化日志
    this.error(`FATAL: ${message}`, error, { ...context, fatal: true });
  }

  error(message: string, error?: Error, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.output(this.formatMessage(LogLevel.ERROR, message, context, error));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.output(this.formatMessage(LogLevel.WARN, message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.output(this.formatMessage(LogLevel.INFO, message, context));
    }
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.output(this.formatMessage(LogLevel.DEBUG, message, context));
    }
  }

  trace(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.TRACE)) {
      this.output(this.formatMessage(LogLevel.TRACE, message, context));
    }
  }

  // 便捷方法
  http(method: string, url: string, status: number, duration: number, context?: LogContext): void {
    this.info(`HTTP ${method} ${url}`, {
      ...context,
      httpMethod: method,
      httpUrl: url,
      httpStatus: status,
      duration,
    });
  }

  auth(event: string, success: boolean, context?: LogContext): void {
    const level = success ? LogLevel.INFO : LogLevel.WARN;
    const message = `Auth ${event}: ${success ? 'SUCCESS' : 'FAILED'}`;
    
    if (level === LogLevel.INFO) {
      this.info(message, { ...context, authEvent: event, authSuccess: success });
    } else {
      this.warn(message, { ...context, authEvent: event, authSuccess: success });
    }
  }

  tool(toolName: string, success: boolean, duration: number, context?: LogContext): void {
    const message = `Tool ${toolName}: ${success ? 'SUCCESS' : 'FAILED'}`;
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    
    if (level === LogLevel.INFO) {
      this.info(message, { ...context, toolName, toolSuccess: success, duration });
    } else {
      this.error(message, undefined, { ...context, toolName, toolSuccess: success, duration });
    }
  }

  // 性能监控
  performance(operation: string, duration: number, context?: LogContext): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.DEBUG; // 5秒以上警告
    const message = `Performance: ${operation} took ${duration}ms`;
    
    if (level === LogLevel.WARN) {
      this.warn(message, { ...context, operation, duration, performanceIssue: true });
    } else {
      this.debug(message, { ...context, operation, duration });
    }
  }

  // 安全事件
  security(event: string, severity: 'low' | 'medium' | 'high', context?: LogContext): void {
    const message = `Security Event: ${event}`;
    const level = severity === 'high' ? LogLevel.ERROR : LogLevel.WARN;
    
    if (level === LogLevel.ERROR) {
      this.error(message, undefined, { ...context, securityEvent: event, severity });
    } else {
      this.warn(message, { ...context, securityEvent: event, severity });
    }
  }
}

// 导出单例实例
export const logger = new Logger();

// 导出便捷函数
export const log = {
  error: (message: string, error?: Error, context?: LogContext) => logger.error(message, error, context),
  warn: (message: string, context?: LogContext) => logger.warn(message, context),
  info: (message: string, context?: LogContext) => logger.info(message, context),
  debug: (message: string, context?: LogContext) => logger.debug(message, context),
  trace: (message: string, context?: LogContext) => logger.trace(message, context),
  http: (method: string, url: string, status: number, duration: number, context?: LogContext) => 
    logger.http(method, url, status, duration, context),
  auth: (event: string, success: boolean, context?: LogContext) => logger.auth(event, success, context),
  tool: (toolName: string, success: boolean, duration: number, context?: LogContext) => 
    logger.tool(toolName, success, duration, context),
  performance: (operation: string, duration: number, context?: LogContext) => 
    logger.performance(operation, duration, context),
  security: (event: string, severity: 'low' | 'medium' | 'high', context?: LogContext) => 
    logger.security(event, severity, context),
  // Remote MCP Server 专用方法
  serverStatus: (message: string, context?: LogContext) => logger.serverStatus(message, context),
  fatal: (message: string, error?: Error, context?: LogContext) => logger.fatal(message, error, context),
};
