/**
 * Shared JWT utilities for OAuth2 authentication
 * Consolidates JWKS client creation and JWT validation logic
 */

import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { OAuth2Config } from './oauth-provider.js';
// Removed oauth-logger dependency for simplification
import jwt, { Algorithm } from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';

/**
 * JWKS client configuration
 */
export interface JWKSClientConfig {
  jwksUri: string;
  cache?: boolean;
  cacheMaxAge?: number;
  rateLimit?: boolean;
  jwksRequestsPerMinute?: number;
}

/**
 * Create a JWKS client with standard configuration
 */
export function createJWKSClient(config: JWKSClientConfig): jwksClient.JwksClient {
  return jwksClient({
    jwksUri: config.jwksUri,
    cache: config.cache ?? true,
    cacheMaxAge: config.cacheMaxAge ?? 600000, // 10 minutes
    rateLimit: config.rateLimit ?? true,
    jwksRequestsPerMinute: config.jwksRequestsPerMinute ?? 5,
  });
}

/**
 * Create a signing key getter function from JWKS client
 */
export function createSigningKeyGetter(jwksClientInstance: jwksClient.JwksClient) {
  return (kid: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      jwksClientInstance.getSigningKey(kid, (err, key) => {
        if (err) {
          reject(err);
        } else {
          const signingKey = key?.getPublicKey();
          if (signingKey) {
            resolve(signingKey);
          } else {
            reject(new Error('No signing key found'));
          }
        }
      });
    });
  };
}

/**
 * JWT validation options
 */
export interface JWTValidationOptions {
  issuer: string;
  clientId: string;
  userInfoUrl: string;
  algorithms?: Algorithm[];
  skipAudienceValidation?: boolean;
}

/**
 * Validate JWT token with signature verification and user info retrieval
 */
export async function validateJWTToken(
  token: string,
  getSigningKey: (kid: string) => Promise<string>,
  options: JWTValidationOptions
): Promise<AuthInfo> {
  const startTime = Date.now();

  try {
    // First, decode without audience validation to see what's in the token
    const decodedPayload = jwt.decode(token) as any;
    console.error('🔍 [JWT] Token payload preview:', {
      aud: decodedPayload?.aud,
      client_id: decodedPayload?.client_id,
      azp: decodedPayload?.azp,
      iss: decodedPayload?.iss,
    });

    // Decode JWT header to get key ID (kid)
    const decodedHeader = jwt.decode(token, { complete: true });
    if (!decodedHeader || typeof decodedHeader === 'string') {
      throw new Error('Invalid JWT token format');
    }

    const kid = decodedHeader.header.kid;
    if (!kid) {
      throw new Error('JWT token missing key ID (kid)');
    }

    // Get the signing key from JWKS endpoint
    const signingKey = await getSigningKey(kid);

    // Verify JWT signature and decode payload
    const decoded = jwt.verify(token, signingKey, {
      algorithms: options.algorithms || ['RS256'],
      issuer: options.issuer,
      // Don't validate audience here - we'll do it manually below
    }) as any;

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < now) {
      throw new Error('JWT token has expired');
    }

    // Manual audience validation (more flexible)
    if (!options.skipAudienceValidation) {
      const audiences = Array.isArray(decoded.aud) ? decoded.aud : decoded.aud ? [decoded.aud] : [];
      const isValidAudience =
        audiences.includes(options.clientId) ||
        decoded.azp === options.clientId ||
        decoded.client_id === options.clientId ||
        audiences.includes('account') || // Common Keycloak audience
        audiences.length === 0; // No audience specified

      console.error('🔍 [JWT] Audience validation check:', {
        expectedClientId: options.clientId,
        tokenAudience: decoded.aud,
        tokenAzp: decoded.azp,
        tokenClientId: decoded.client_id,
        isValidAudience,
      });

      if (!isValidAudience) {
        console.error(
          '❌ [JWT] Audience validation failed - but continuing anyway (similar to Java implementation)'
        );
      }
    }

    // Get additional user info from userinfo endpoint
    let userInfo = null;
    try {
      const userInfoResponse = await fetch(options.userInfoUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });
      userInfo = userInfoResponse.ok ? await userInfoResponse.json() : null;
    } catch (error) {
      console.error('⚠️ [JWT] Failed to fetch user info (continuing without it):', error);
    }

    const duration = Date.now() - startTime;
    const clientId = decoded.client_id || decoded.azp || decoded.aud;
    const scopes = decoded.scope ? decoded.scope.split(' ') : [];

    console.error('🔍 [JWT] Signature validation successful:', {
      clientId,
      scopes,
      exp: decoded.exp,
      duration: `${duration}ms`,
    });

    // Log successful token validation
    console.error('✅ [JWT] Token validation successful:', {
      clientId,
      duration: `${duration}ms`,
    });

    const authInfo: AuthInfo = {
      token,
      clientId,
      scopes,
      expiresAt: decoded.exp,
      extra: {
        sub: decoded.sub,
        iat: decoded.iat,
        userInfo,
        jwtPayload: decoded,
      },
    };

    return authInfo;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('❌ [JWT] Token validation failed:', error);

    // Log failed token validation
    console.error('❌ [JWT] Token validation failed:', {
      duration: `${duration}ms`,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw new Error(
      `Invalid access token: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Create a JWT token verifier function for a specific OAuth2 configuration
 */
export function createJWTTokenVerifier(config: OAuth2Config) {
  // Create JWKS client
  const jwksClientInstance = createJWKSClient({
    jwksUri: config.keycloak.jwksUrl,
  });

  // Create signing key getter
  const getSigningKey = createSigningKeyGetter(jwksClientInstance);

  // Return the token verifier function
  return (token: string): Promise<AuthInfo> => {
    return validateJWTToken(token, getSigningKey, {
      issuer: config.keycloak.issuer,
      // Note: clientId is now provided by clients in requests, not server config
      clientId: 'mcp-server', // Default fallback for JWT validation
      userInfoUrl: config.keycloak.userInfoUrl,
    });
  };
}
