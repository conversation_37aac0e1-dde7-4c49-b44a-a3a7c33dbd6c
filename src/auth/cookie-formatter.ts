import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { getGlobalConfig } from '../config/global-config.js';
import { globalMcpUserTokenService } from './permission-service.js';

/**
 * Provider for authentication cookies that can use either:
 * 1. McpUserToken from JWT authentication (preferred)
 * 2. Static AUTH_COOKIES from environment (fallback)
 */

export interface AuthCookiesContext {
  authInfo?: AuthInfo;
  fallbackToEnv?: boolean;
  serviceModule?: string; // Service module name for determining clientId
}

/**
 * Get authentication cookies for API requests
 * Priority:
 * 1. McpUserToken from JWT authentication via permission service (PRODUCTION)
 * 2. Static AUTH_COOKIES from environment - DEBUGGING ONLY
 */
export async function getAuthCookies(context?: AuthCookiesContext): Promise<string> {
  // Try to use McpUserToken from JWT authentication first (PRODUCTION)
  if (context?.authInfo) {
    try {
      console.error('🔐 [AuthCookies] Using JWT authentication (PRODUCTION)');

      // Determine the correct clientId for the service
      let clientId = context.authInfo.clientId as string;
      if (context.serviceModule) {
        const { getServiceClientId } = await import('../adapters/service-adapter.js');
        const serviceClientId = getServiceClientId(context.serviceModule);
        if (serviceClientId) {
          clientId = serviceClientId;
          console.error('🔐 [AuthCookies] Using service-specific clientId:', {
            serviceModule: context.serviceModule,
            clientId,
            originalClientId: context.authInfo.clientId,
          });
        }
      }

      // Get McpUserToken directly (with built-in caching)
      const mcpUserToken = await globalMcpUserTokenService.obtainMcpUserToken(
        context.authInfo.token,
        clientId
      );

      // Format as cookie
      const cookieString = globalMcpUserTokenService.formatAsCookie(mcpUserToken, clientId);

      console.error('✅ [AuthCookies] JWT authentication successful:', {
        serviceModule: context.serviceModule || 'default',
        clientId,
        cookiePrefix: cookieString.substring(0, 30) + '...',
      });

      return cookieString;
    } catch (error) {
      console.error('❌ [AuthCookies] JWT authentication failed:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        serviceModule: context.serviceModule,
      });

      // Don't fall back to environment cookies on permission service errors in production
      throw error;
    }
  }

  // No JWT authentication context - check fallback options
  console.error('🚨 [AuthCookies] No JWT authentication context provided');

  // Fallback to environment AUTH_COOKIES (DEBUGGING ONLY)
  if (context?.fallbackToEnv !== false) {
    const env = getGlobalConfig();
    if (env.AUTH_COOKIES) {
      console.error('� [AuthCookies] Using environment AUTH_COOKIES fallback');
      return env.AUTH_COOKIES;
    }
  }

  console.error('❌ [AuthCookies] No authentication cookies available');
  console.error('💡 [AuthCookies] In production: Provide valid JWT token to obtain McpUserToken');
  console.error('💡 [AuthCookies] For debugging: Set AUTH_COOKIES environment variable');
  throw new Error(
    'No authentication cookies available. Either provide JWT token or set AUTH_COOKIES environment variable.'
  );
}

/**
 * Check if authentication cookies are available
 */
export async function hasAuthCookies(context?: AuthCookiesContext): Promise<boolean> {
  try {
    await getAuthCookies(context);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get authentication cookies with detailed logging
 */
export async function getAuthCookiesWithLogging(context?: AuthCookiesContext): Promise<string> {
  const startTime = Date.now();

  try {
    const cookies = await getAuthCookies(context);
    const duration = Date.now() - startTime;

    console.error('✅ [AuthCookies] Successfully obtained cookies:', {
      source: context?.authInfo ? 'JWT+PermissionService' : 'Environment',
      hasCookies: !!cookies,
      cookieLength: cookies.length,
      duration: `${duration}ms`,
    });

    return cookies;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('❌ [AuthCookies] Failed to obtain cookies:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration: `${duration}ms`,
    });
    throw error;
  }
}

/**
 * Create auth cookies context from request
 * This can be used in middleware to pass auth context to service adapters
 * @param authInfo - Authentication information
 * @param serviceModule - Optional service module name to determine the correct clientId
 */
export function createAuthCookiesContext(
  authInfo?: AuthInfo,
  serviceModule?: string
): AuthCookiesContext {
  return {
    authInfo,
    fallbackToEnv: true,
    serviceModule, // Store service module for later use
  };
}
