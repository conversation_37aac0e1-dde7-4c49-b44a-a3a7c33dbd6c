import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';

/**
 * Service for managing MCP User Tokens from the permission service
 * Based on reference/permission-service/.../UserAuthController.java
 */

interface McpUserTokenConfig {
  permissionServiceBaseUrl: string;
  permissionServiceClientId: string;
}

interface McpUserTokenResponse {
  code: number;
  message: string;
  data: string; // The pm_mcp_user_token
}

/**
 * Parsed session information from McpUserToken
 * Session format: {uuid}@{timestamp}.{userId}.{timeout}
 */
interface ParsedSessionInfo {
  ssoUUID: string;
  loginTime: number;
  userId: string;
  sessionTimeout: number;
  expiresAt: Date;
  isExpired: boolean;
}

// In-memory session storage for McpUserTokens (per JWT token)
// In production, this should be replaced with Redis or similar
const mcpUserTokenSessions = new Map<
  string,
  {
    mcpUserToken: string;
    userId: string;
    clientId: string;
    jwtToken: string; // Store the JWT token for refresh
    expiresAt: number;
    lastRefresh: number;
  }
>();

export class McpUserTokenService {
  private config: McpUserTokenConfig;

  constructor() {
    this.config = {
      permissionServiceBaseUrl:
        process.env.PERMISSION_SERVICE_BASE_URL ||
        'https://mpp-dev-i.ingka-dt.cn/permission-service',
      permissionServiceClientId: process.env.PERMISSION_SERVICE_CLIENT_ID || 'mcp-mpc-odi',
    };

    console.error('🔐 McpUserTokenService initialized:');
    console.error(`   Permission Service: ${this.config.permissionServiceBaseUrl}`);
    console.error(`   Client ID: ${this.config.permissionServiceClientId}`);
  }

  /**
   * Obtain McpUserToken from permission service using JWT token (with built-in caching)
   * Based on /getMcpUserToken endpoint in UserAuthController.java
   * @param jwtToken - JWT token for authentication
   * @param clientId - Optional service-specific clientId (defaults to configured clientId)
   */
  async obtainMcpUserToken(jwtToken: string, clientId?: string): Promise<string> {
    const effectiveClientId = clientId || this.config.permissionServiceClientId;

    // Create cache key based on token and clientId
    const cacheKey = `${this.simpleHash(jwtToken)}_${effectiveClientId}`;

    // Check cache first
    const cached = mcpUserTokenSessions.get(cacheKey);
    if (cached && Date.now() <= cached.expiresAt) {
      console.error('🔄 [McpUserToken] Using cached token:', {
        clientId: effectiveClientId,
        tokenPrefix: cached.mcpUserToken.substring(0, 20) + '...',
      });
      return cached.mcpUserToken;
    }

    // Not in cache, fetch from permission service
    const startTime = Date.now();
    const requestUrl = `${this.config.permissionServiceBaseUrl}/user/auth/getMcpUserToken`;

    try {
      console.error('🔍 [McpUserToken] Requesting token from permission service...', {
        url: requestUrl,
        clientId: effectiveClientId,
        jwtTokenPrefix: jwtToken.substring(0, 20) + '...',
        usingServiceSpecificClientId: !!clientId,
        originalClientId: clientId ? this.config.permissionServiceClientId : 'N/A',
      });

      console.error('📡 [McpUserToken] Making HTTP request to permission service:', {
        method: 'POST',
        url: requestUrl,
        headers: {
          Authorization: `Bearer ${jwtToken.substring(0, 20)}...`,
          clientId: effectiveClientId,
          'Content-Type': 'application/json',
        },
      });

      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${jwtToken}`,
          clientId: effectiveClientId,
          'Content-Type': 'application/json',
        },
      });

      const duration = Date.now() - startTime;

      // Log response details for debugging
      console.error('📡 [McpUserToken] Permission service response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        duration: `${duration}ms`,
      });

      if (!response.ok) {
        // Try to get response body for more details
        let responseBody = '';
        try {
          responseBody = await response.text();
          console.error('📄 [McpUserToken] Error response body:', responseBody);
        } catch (bodyError) {
          console.error('⚠️ [McpUserToken] Could not read error response body:', bodyError);
        }

        throw new Error(
          `Permission service request failed: ${response.status} ${response.statusText}${
            responseBody ? ` - Body: ${responseBody}` : ''
          }`
        );
      }

      const result = (await response.json()) as McpUserTokenResponse;

      console.error('📋 [McpUserToken] Permission service response data:', {
        code: result.code,
        message: result.message,
        hasData: !!result.data,
        dataLength: result.data?.length || 0,
      });

      if (result.code !== 200) {
        throw new Error(`Permission service error: ${result.message} (code: ${result.code})`);
      }

      const mcpUserToken = result.data;

      console.error('✅ [McpUserToken] Successfully obtained token:', {
        duration: `${duration}ms`,
        tokenPrefix: mcpUserToken.substring(0, 20) + '...',
        tokenLength: mcpUserToken.length,
      });

      // Cache the token for future use (default 1 hour)
      const expiresAt = Date.now() + 60 * 60 * 1000;
      mcpUserTokenSessions.set(cacheKey, {
        mcpUserToken,
        userId: 'jwt-user', // We don't have user ID from JWT in this context
        clientId: effectiveClientId,
        jwtToken,
        expiresAt,
        lastRefresh: Date.now(),
      });

      return mcpUserToken;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Enhanced error logging
      const errorDetails: Record<string, unknown> = {
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: `${duration}ms`,
        requestUrl,
        clientId: effectiveClientId,
        jwtTokenPrefix: jwtToken.substring(0, 20) + '...',
      };

      // Add additional error context if available
      if (error instanceof Error) {
        errorDetails.errorName = error.name;
        errorDetails.errorStack = error.stack?.split('\n').slice(0, 3).join('\n'); // First 3 lines of stack
      }

      // Check if it's a network error
      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorDetails.errorType = 'NETWORK_ERROR';
        errorDetails.suggestion = 'Check network connectivity and permission service URL';
      }

      console.error('❌ [McpUserToken] Failed to obtain token:', errorDetails);
      throw error;
    }
  }

  /**
   * Store McpUserToken in session associated with the JWT token
   */
  storeMcpUserTokenInSession(authInfo: AuthInfo, mcpUserToken: string): void {
    const sessionKey = this.generateSessionKey(authInfo);

    // Parse the McpUserToken to get the actual expiration time
    let expiresAt: number;
    try {
      const sessionInfo = this.parseSessionInfo(mcpUserToken);
      expiresAt = sessionInfo.expiresAt.getTime();

      console.error('🕒 [McpUserToken] Using expiration from token session:', {
        tokenExpiration: sessionInfo.expiresAt.toISOString(),
        jwtExpiration: authInfo.expiresAt
          ? new Date(authInfo.expiresAt * 1000).toISOString()
          : 'none',
      });
    } catch (error) {
      // Fallback to JWT expiration or default
      expiresAt = authInfo.expiresAt ? authInfo.expiresAt * 1000 : Date.now() + 24 * 60 * 60 * 1000; // 24 hours default

      console.error('⚠️ [McpUserToken] Failed to parse token expiration, using fallback:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        fallbackExpiration: new Date(expiresAt).toISOString(),
      });
    }

    mcpUserTokenSessions.set(sessionKey, {
      mcpUserToken,
      userId: (authInfo.extra?.sub as string) || 'unknown',
      clientId: authInfo.clientId,
      jwtToken: authInfo.token,
      expiresAt,
      lastRefresh: Date.now(),
    });

    console.error('💾 [McpUserToken] Stored in session:', {
      sessionKey: sessionKey.substring(0, 20) + '...',
      userId: authInfo.extra?.sub || 'unknown',
      clientId: authInfo.clientId,
      expiresAt: new Date(expiresAt).toISOString(),
    });
  }

  /**
   * Retrieve McpUserToken from session
   */
  getMcpUserTokenFromSession(authInfo: AuthInfo): string | null {
    const sessionKey = this.generateSessionKey(authInfo);
    const session = mcpUserTokenSessions.get(sessionKey);

    if (!session) {
      console.error(
        '🔍 [McpUserToken] No session found for key:',
        sessionKey.substring(0, 20) + '...'
      );
      return null;
    }

    // Check if token is expired
    if (Date.now() > session.expiresAt) {
      console.error(
        '⏰ [McpUserToken] Session expired, removing:',
        sessionKey.substring(0, 20) + '...'
      );
      mcpUserTokenSessions.delete(sessionKey);
      return null;
    }

    console.error('✅ [McpUserToken] Retrieved from session:', {
      sessionKey: sessionKey.substring(0, 20) + '...',
      userId: session.userId,
      clientId: session.clientId,
    });

    return session.mcpUserToken;
  }

  /**
   * Refresh McpUserToken when it expires (called on 401/403 errors)
   */
  async refreshMcpUserToken(authInfo: AuthInfo): Promise<string> {
    const sessionKey = this.generateSessionKey(authInfo);
    console.error(
      '🔄 [McpUserToken] Refreshing expired token for session:',
      sessionKey.substring(0, 20) + '...'
    );

    // Remove the expired token from session
    mcpUserTokenSessions.delete(sessionKey);

    // Obtain a new token
    const newMcpUserToken = await this.obtainMcpUserToken(authInfo.token);

    // Store the new token in session
    this.storeMcpUserTokenInSession(authInfo, newMcpUserToken);

    console.error('✅ [McpUserToken] Token refreshed successfully');
    return newMcpUserToken;
  }

  /**
   * Check if a token refresh is needed based on error response
   */
  shouldRefreshToken(error: unknown): boolean {
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;

      // Check for HTTP status codes that indicate token expiration
      if (errorObj.status === 401 || errorObj.status === 403) {
        return true;
      }

      // Check for error messages that indicate token expiration
      const message = (errorObj.message as string) || error.toString();
      if (
        message.includes('401') ||
        message.includes('403') ||
        message.includes('Unauthorized') ||
        message.includes('Forbidden') ||
        (message.includes('token') && (message.includes('expired') || message.includes('invalid')))
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate a unique session key based on auth info
   * @param authInfo - Authentication information
   * @param serviceModule - Optional service module for service-specific caching
   */
  private generateSessionKey(authInfo: AuthInfo, serviceModule?: string): string {
    // Use a combination of token hash, client ID, user ID, and service module for uniqueness
    const tokenHash = this.simpleHash(authInfo.token);
    const userId = authInfo.extra?.sub || 'unknown';
    const servicePrefix = serviceModule ? `${serviceModule}_` : '';
    return `mcp_session_${servicePrefix}${authInfo.clientId}_${userId}_${tokenHash}`;
  }

  /**
   * Simple hash function for generating session keys
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Parse McpUserToken to extract session information
   * Session format: {uuid}@{timestamp}.{userId}.{timeout}
   */
  parseSessionInfo(mcpUserToken: string): ParsedSessionInfo {
    try {
      // Decode base64 token
      const sessionString = Buffer.from(mcpUserToken, 'base64').toString();
      console.error('🔍 [McpUserToken] Parsing session string:', {
        sessionStringPrefix: sessionString.substring(0, 50) + '...',
        sessionStringLength: sessionString.length,
      });

      // Parse session format: {uuid}@{timestamp}.{userId}.{timeout}
      const parts = sessionString.split('@');
      if (parts.length !== 2) {
        throw new Error(
          `Invalid session format: expected 2 parts separated by '@', got ${parts.length}`
        );
      }

      const [ssoUUID, rightPart] = parts;
      const rightParts = rightPart.split('.');

      if (rightParts.length < 3) {
        throw new Error(
          `Invalid session structure: expected at least 3 parts after '@', got ${rightParts.length}`
        );
      }

      const loginTime = parseInt(rightParts[0]);
      const userId = rightParts[1];
      const sessionTimeout = parseInt(rightParts[rightParts.length - 1]);

      // Validate parsed values
      if (isNaN(loginTime)) {
        throw new Error(`Invalid login time: ${rightParts[0]}`);
      }
      if (isNaN(sessionTimeout)) {
        throw new Error(`Invalid session timeout: ${rightParts[rightParts.length - 1]}`);
      }

      // Calculate expiration
      const expiresAt = new Date(loginTime + sessionTimeout * 1000);
      const isExpired = new Date() > expiresAt;

      const sessionInfo: ParsedSessionInfo = {
        ssoUUID,
        loginTime,
        userId,
        sessionTimeout,
        expiresAt,
        isExpired,
      };

      console.error('✅ [McpUserToken] Session parsed successfully:', {
        ssoUUID: ssoUUID.substring(0, 8) + '...',
        userId: userId.substring(0, 8) + '...',
        loginTime: new Date(loginTime).toISOString(),
        sessionTimeout: `${sessionTimeout}s`,
        expiresAt: expiresAt.toISOString(),
        isExpired,
      });

      return sessionInfo;
    } catch (error) {
      console.error('❌ [McpUserToken] Failed to parse session info:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tokenPrefix: mcpUserToken.substring(0, 20) + '...',
        tokenLength: mcpUserToken.length,
      });
      throw new Error(
        `Failed to parse McpUserToken session: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Determine the correct cookie name based on environment and clientId
   * Non-production: test_<clientId> (from SERVICE_CLIENT_ID_MAPPING)
   * Production: <clientId> (from SERVICE_CLIENT_ID_MAPPING)
   */
  getCookieName(clientId: string): string {
    // Check if we're in a testing environment
    const isTestEnvironment =
      process.env.NODE_ENV === 'dev' ||
      process.env.NODE_ENV === 'test' ||
      this.config.permissionServiceBaseUrl.includes('dev') ||
      this.config.permissionServiceBaseUrl.includes('test');

    // Use the clientId directly (it should come from SERVICE_CLIENT_ID_MAPPING)
    const baseCookieName = clientId;

    // Add test prefix if in testing environment
    const cookieName = isTestEnvironment ? `test_${baseCookieName}` : baseCookieName;

    console.error('🍪 [McpUserToken] Determined cookie name:', {
      clientId,
      cookieName,
      isTestEnvironment,
      baseCookieName,
      permissionServiceUrl: this.config.permissionServiceBaseUrl,
    });

    return cookieName;
  }

  /**
   * Format McpUserToken as a cookie string
   */
  formatAsCookie(mcpUserToken: string, clientId: string): string {
    const cookieName = this.getCookieName(clientId);
    const cookieString = `${cookieName}=${mcpUserToken}`;

    console.error('🍪 [McpUserToken] Formatted as cookie:', {
      cookieName,
      tokenPrefix: mcpUserToken.substring(0, 20) + '...',
      cookieLength: cookieString.length,
    });

    return cookieString;
  }

  /**
   * Check if McpUserToken is expired by parsing its session info
   */
  isTokenExpired(mcpUserToken: string): boolean {
    try {
      const sessionInfo = this.parseSessionInfo(mcpUserToken);
      return sessionInfo.isExpired;
    } catch (error) {
      console.error('⚠️ [McpUserToken] Failed to check token expiration, assuming expired:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tokenPrefix: mcpUserToken.substring(0, 20) + '...',
      });
      return true; // Assume expired if we can't parse it
    }
  }

  /**
   * Get a fresh cookie string for the given authInfo
   * This will either use cached token or obtain a new one if needed
   */
  async getFreshCookieString(authInfo: AuthInfo): Promise<string> {
    const mcpUserToken = await this.obtainMcpUserToken(authInfo.token, authInfo.clientId);
    return this.formatAsCookie(mcpUserToken, authInfo.clientId);
  }

  /**
   * Get cookie string from current session (no refresh)
   * Returns null if no valid token is available
   */
  getCookieStringFromSession(authInfo: AuthInfo): string | null {
    const mcpUserToken = this.getMcpUserTokenFromSession(authInfo);
    if (!mcpUserToken) {
      return null;
    }

    // Check if token is expired
    if (this.isTokenExpired(mcpUserToken)) {
      console.error('⏰ [McpUserToken] Token in session is expired');
      return null;
    }

    return this.formatAsCookie(mcpUserToken, authInfo.clientId);
  }

  /**
   * Clear expired sessions (cleanup utility)
   */
  clearExpiredSessions(): void {
    const now = Date.now();
    let clearedCount = 0;

    for (const [key, session] of mcpUserTokenSessions.entries()) {
      if (now > session.expiresAt) {
        mcpUserTokenSessions.delete(key);
        clearedCount++;
      }
    }

    if (clearedCount > 0) {
      console.error(`🧹 [McpUserToken] Cleared ${clearedCount} expired sessions`);
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(): { totalSessions: number; activeSessions: number } {
    const now = Date.now();
    let activeSessions = 0;

    for (const session of mcpUserTokenSessions.values()) {
      if (now <= session.expiresAt) {
        activeSessions++;
      }
    }

    return {
      totalSessions: mcpUserTokenSessions.size,
      activeSessions,
    };
  }
}

// Global instance
export const globalMcpUserTokenService = new McpUserTokenService();

// Cleanup expired sessions every 30 minutes
setInterval(
  () => {
    globalMcpUserTokenService.clearExpiredSessions();
  },
  30 * 60 * 1000
);
