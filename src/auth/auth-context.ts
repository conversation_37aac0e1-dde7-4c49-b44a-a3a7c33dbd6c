import { Request, Response, NextFunction } from 'express';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';

/**
 * Simple request context for auth information
 */
let currentAuthInfo: AuthInfo | null = null;

/**
 * Get current auth info from request context
 */
export function getCurrentAuthInfo(): AuthInfo | null {
  return currentAuthInfo;
}

/**
 * Get auth cookies from request context for a specific service module
 */
export async function getAuthCookiesFromContext(serviceModule?: string): Promise<string | null> {
  const authInfo = getCurrentAuthInfo();

  if (!authInfo) {
    console.error(
      '🚨 [RequestContext] No auth info available for service:',
      serviceModule || 'default'
    );
    return null;
  }

  // Try to get McpUserToken for the specific service module
  if (serviceModule) {
    try {
      console.error('🔐 [RequestContext] Obtaining McpUserToken for service:', serviceModule);

      // Import the service dynamically to avoid circular dependencies
      const { globalMcpUserTokenService } = await import('./permission-service.js');

      // Import the service adapter helper to get the correct clientId for token exchange
      const { getServiceClientId } = await import('../adapters/service-adapter.js');
      const clientId = getServiceClientId(serviceModule);

      if (!clientId) {
        console.error('❌ [RequestContext] No clientId found for service module:', serviceModule);
        return null;
      }

      // Get service-specific McpUserToken using the service's clientId
      const mcpUserToken = await globalMcpUserTokenService.obtainMcpUserToken(
        authInfo.token,
        clientId
      );

      // Format as proper cookie
      return globalMcpUserTokenService.formatAsCookie(mcpUserToken, clientId);
    } catch (error) {
      console.error('❌ [RequestContext] Failed to obtain McpUserToken:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        serviceModule,
      });
      return null;
    }
  }

  // No service module specified, cannot obtain service-specific token
  console.error('🔍 [RequestContext] No service module specified, cannot obtain McpUserToken');
  return null;
}

/**
 * Simple middleware to extract auth info from request
 */
export function createRequestContextMiddleware() {
  return (req: Request & { authInfo?: AuthInfo }, _res: Response, next: NextFunction) => {
    // Store auth info globally (simplified)
    if (req.authInfo) {
      currentAuthInfo = req.authInfo;
      console.error('✅ [RequestContext] JWT auth available:', {
        clientId: req.authInfo.clientId,
        scopes: req.authInfo.scopes?.slice(0, 3),
      });
    } else {
      currentAuthInfo = null;
      console.error('⚠️ [RequestContext] No JWT auth in request:', {
        path: req.path,
        method: req.method,
        hasAuthHeader: !!req.headers.authorization,
      });
    }

    next();
  };
}
