import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { ToolConfig, ToolRiskLevel } from '../adapters/service-adapter.js';

/**
 * Tool elicitation utilities for MCP clients
 */

export interface ElicitationCheckResult {
  success: boolean;
  error?: string;
  shouldConfirm?: boolean;
  riskLevel?: ToolRiskLevel;
}

/**
 * Get elicitation guidance for a specific tool
 */
export async function getToolElicitationGuidance(
  authInfo: AuthInfo | null,
  toolConfig: ToolConfig
): Promise<ElicitationCheckResult> {
  // Allow test tools without authentication for development
  if (toolConfig.category === 'testing') {
    return {
      success: true,
      shouldConfirm: false,
      riskLevel: 'safe' as ToolRiskLevel,
    };
  }

  // Basic authentication check
  if (!authInfo) {
    return {
      success: false,
      error: 'Authentication required for this tool',
    };
  }

  // Return elicitation guidance based on tool metadata
  return {
    success: true,
    shouldConfirm: toolConfig.requiresConfirmation || false,
    riskLevel: toolConfig.riskLevel || ('safe' as ToolRiskLevel),
  };
}

/**
 * Filter tools based on elicitation requirements
 */
export async function filterToolsByElicitation(
  tools: ToolConfig[],
  authInfo: AuthInfo | null
): Promise<ToolConfig[]> {
  const results = await Promise.all(
    tools.map(async tool => {
      const elicitationCheck = await getToolElicitationGuidance(authInfo, tool);
      return elicitationCheck.success ? tool : null;
    })
  );

  return results.filter((tool): tool is ToolConfig => tool !== null);
}

/**
 * Get tools categorized by capability
 */
export function categorizeToolsByCapability(tools: ToolConfig[]): {
  readonly: ToolConfig[];
  write: ToolConfig[];
  destructive: ToolConfig[];
} {
  return {
    readonly: tools.filter(t => t.capability === 'readonly'),
    write: tools.filter(t => t.capability === 'write'),
    destructive: tools.filter(t => t.capability === 'destructive'),
  };
}

/**
 * Get tools categorized by risk level
 */
export function categorizeToolsByRisk(tools: ToolConfig[]): {
  safe: ToolConfig[];
  moderate: ToolConfig[];
  high: ToolConfig[];
} {
  return {
    safe: tools.filter(t => t.riskLevel === 'safe'),
    moderate: tools.filter(t => t.riskLevel === 'moderate'),
    high: tools.filter(t => t.riskLevel === 'high'),
  };
}

/**
 * Get elicitation summary for tools
 */
export function getToolElicitationSummary(tools: ToolConfig[]): {
  totalTools: number;
  byCapability: { readonly: number; write: number; destructive: number };
  byRisk: { safe: number; moderate: number; high: number };
  requiresConfirmation: number;
  safeToExecute: number;
} {
  const byCapability = categorizeToolsByCapability(tools);
  const byRisk = categorizeToolsByRisk(tools);

  return {
    totalTools: tools.length,
    byCapability: {
      readonly: byCapability.readonly.length,
      write: byCapability.write.length,
      destructive: byCapability.destructive.length,
    },
    byRisk: {
      safe: byRisk.safe.length,
      moderate: byRisk.moderate.length,
      high: byRisk.high.length,
    },
    requiresConfirmation: tools.filter(t => t.requiresConfirmation).length,
    safeToExecute: tools.filter(t => t.riskLevel === 'safe' && !t.requiresConfirmation).length,
  };
}
