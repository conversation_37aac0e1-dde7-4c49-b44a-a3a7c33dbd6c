/**
 * 🔐 Unified Authentication Module
 *
 * Consolidates all authentication logic:
 * - OAuth2 token validation (connection + per-request)
 * - <PERSON>ie authentication
 * - Token caching
 * - MCP compliance
 */

import { Request, Response, NextFunction } from 'express';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { mcpAuthRouter } from '@modelcontextprotocol/sdk/server/auth/router.js';
import { requireBearerAuth } from '@modelcontextprotocol/sdk/server/auth/middleware/bearerAuth.js';
import type { Application } from 'express';
import { getGlobalConfig } from '../config/global-config.js';
import { globalTokenCache } from './jwt-cache.js';
import { OAuth2Config, createOAuth2Client<PERSON>rovider } from './oauth-provider.js';
import { createJWTTokenVerifier } from './jwt-validator.js';

export interface AuthConfig {
  // Connection auth (initialize requests)
  connectionAuthEnabled: boolean;
  connectionAuthStrict: boolean;
  connectionAuthTestApi: boolean;

  // Per-request auth (MCP compliance)
  perRequestAuthEnabled: boolean;

  // Token caching
  cacheEnabled: boolean;
  cacheMaxAge: number;

  // Debugging
  logValidation: boolean;
}

export interface AuthResult {
  success: boolean;
  authInfo?: AuthInfo;
  error?: string;
  cached?: boolean;
  duration?: number;
}

// Use the global token cache from token-cache.ts instead of local implementation

/**
 * Load unified auth configuration from global config store
 * Uses proper configuration hierarchy: NACOS > Helm > process.env > defaults
 */
export function loadAuthConfig(): AuthConfig {
  const env = getGlobalConfig();
  return {
    connectionAuthEnabled: env.CONNECTION_AUTH_ENABLED,
    connectionAuthStrict: env.CONNECTION_AUTH_STRICT,
    connectionAuthTestApi: env.CONNECTION_AUTH_TEST_API,
    perRequestAuthEnabled: env.PER_REQUEST_AUTH_ENABLED || false,
    cacheEnabled: env.PER_REQUEST_AUTH_CACHE_ENABLED !== false,
    cacheMaxAge: env.PER_REQUEST_AUTH_CACHE_MAX_AGE || 300,
    logValidation: env.PER_REQUEST_AUTH_LOG_VALIDATION || false,
  };
}

/**
 * Validate OAuth2 Bearer token
 */
export async function validateToken(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<AuthResult> {
  const startTime = Date.now();
  const config = loadAuthConfig();

  try {
    // Extract Bearer token
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid Authorization header',
        duration: Date.now() - startTime,
      };
    }

    const token = authHeader.substring(7);

    // Check cache first
    if (config.cacheEnabled) {
      const cachedAuth = globalTokenCache.get(token);
      if (cachedAuth) {
        if (config.logValidation) {
          console.log('🎯 [Auth] Cache hit');
        }
        return {
          success: true,
          authInfo: cachedAuth,
          cached: true,
          duration: Date.now() - startTime,
        };
      }
    }

    // Validate with OAuth2 provider
    if (!oauth2Provider) {
      return {
        success: false,
        error: 'OAuth2 provider not available',
        duration: Date.now() - startTime,
      };
    }

    const authInfo = await oauth2Provider.verifyAccessToken(token);

    // McpUserTokens will be obtained lazily per service module when needed
    // This allows each service to use its specific clientId from SERVICE_CLIENT_ID_MAPPING
    console.error(
      '✅ [Auth] JWT token validated - McpUserTokens will be obtained per service as needed'
    );

    // Cache the result
    if (config.cacheEnabled) {
      globalTokenCache.set(token, authInfo);
    }

    if (config.logValidation) {
      console.log('✅ [Auth] Token validation successful:', {
        clientId: authInfo.clientId,
        scopes: authInfo.scopes?.slice(0, 3),
        hasMcpUserToken: !!authInfo.extra?.mcpUserToken,
        duration: Date.now() - startTime,
      });
    }

    return {
      success: true,
      authInfo,
      cached: false,
      duration: Date.now() - startTime,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    if (config.logValidation) {
      console.log(
        '❌ [Auth] Token validation failed:',
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token validation failed',
      duration,
    };
  }
}

/**
 * Validate cookie authentication (fallback)
 */
export async function validateCookie(req: Request): Promise<AuthResult> {
  const startTime = Date.now();

  try {
    const cookies = req.headers.cookie;
    if (!cookies) {
      return {
        success: false,
        error: 'No authentication cookies provided',
        duration: Date.now() - startTime,
      };
    }

    const ordersCookieMatch = cookies.match(/test_orders-portal=([^;]+)/);
    if (!ordersCookieMatch) {
      return {
        success: false,
        error: 'Missing test_orders-portal cookie',
        duration: Date.now() - startTime,
      };
    }

    const pmUserToken = ordersCookieMatch[1];

    // Basic token format validation
    try {
      const tokenString = Buffer.from(pmUserToken, 'base64').toString();
      const parts = tokenString.split('@');

      if (parts.length !== 2) {
        throw new Error('Invalid token format');
      }

      const [ssoUUID, rightPart] = parts;
      const rightParts = rightPart.split('.');

      if (rightParts.length < 3) {
        throw new Error('Invalid token structure');
      }

      const loginTime = parseInt(rightParts[0]);
      const userId = rightParts[1];
      const sessionTimeout = parseInt(rightParts[rightParts.length - 1]);

      // Check expiration
      const expiresAt = new Date(loginTime + sessionTimeout * 1000);
      if (new Date() > expiresAt) {
        return {
          success: false,
          error: `Token expired at ${expiresAt.toISOString()}`,
          duration: Date.now() - startTime,
        };
      }

      return {
        success: true,
        authInfo: {
          token: pmUserToken,
          clientId: 'cookie-auth',
          scopes: ['profile'],
          expiresAt: Math.floor(expiresAt.getTime() / 1000),
          extra: { userInfo: { userId, ssoUUID } },
        } as AuthInfo,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: `Invalid token format: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Cookie validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: Date.now() - startTime,
    };
  }
}

/**
 * Main authentication function - tries OAuth2 first, then cookie fallback
 */
export async function authenticate(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<AuthResult> {
  const config = loadAuthConfig();

  // Try OAuth2 first
  if (req.headers.authorization) {
    const oauth2Result = await validateToken(req, oauth2Provider);
    if (oauth2Result.success || config.connectionAuthStrict) {
      return oauth2Result;
    }
  }

  // Fallback to cookie auth
  if (req.headers.cookie) {
    return await validateCookie(req);
  }

  return {
    success: false,
    error:
      'No valid authentication provided. Please provide OAuth2 Bearer token or authentication cookies.',
  };
}

/**
 * Express middleware for per-request authentication (MCP compliance)
 */
export function createAuthMiddleware(oauth2Provider?: ProxyOAuthServerProvider) {
  const config = loadAuthConfig();

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip health and metadata endpoints FIRST (before any other checks)
    if (
      ['/health', '/mcp/health', '/.well-known', '/auth/status', '/mcp/capabilities'].some(path =>
        req.path.startsWith(path)
      )
    ) {
      return next();
    }

    // Skip if disabled or not MCP endpoint
    if (!config.perRequestAuthEnabled || !req.path.startsWith('/mcp')) {
      return next();
    }

    const authResult = await authenticate(req, oauth2Provider);

    if (!authResult.success) {
      const errorType = authResult.error?.includes('Missing required scopes')
        ? 'insufficient_scope'
        : 'invalid_token';

      // Simple error response
      res.status(401).json({
        error: errorType,
        error_description: authResult.error || 'Authentication failed',
      });
      return;
    }

    // Attach auth info to request
    (req as any).authInfo = authResult.authInfo; // Fixed: use authInfo instead of auth
    (req as any).authMeta = {
      cached: authResult.cached,
      duration: authResult.duration,
    };

    next();
  };
}

/**
 * Get authentication status and metrics
 */
export function getAuthStatus() {
  const config = loadAuthConfig();
  return {
    config,
    cache: {
      enabled: config.cacheEnabled,
      size: globalTokenCache.getStats().size,
      maxSize: globalTokenCache.getStats().maxSize,
      hitRate: globalTokenCache.getStats().hitRate,
      maxAge: config.cacheMaxAge,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Clear token cache
 */
export function clearAuthCache() {
  globalTokenCache.clear();
}

/**
 * Set up MCP OAuth2 authentication following the reference implementation pattern
 * This creates the OAuth2 provider and mounts the auth router
 */
export function setupMCPAuth(app: Application, config: OAuth2Config) {
  // Create JWT token verifier using shared utilities
  const verifyAccessToken = createJWTTokenVerifier(config);

  // Create cached token verification function
  const cachedVerifyAccessToken = async (token: string): Promise<AuthInfo> => {
    // Try cache first
    const cached = globalTokenCache.get(token);
    if (cached) {
      console.log(`🎯 [TokenCache] Cache hit for token`);
      return cached;
    }

    console.log(`🔍 [TokenCache] Cache miss, validating with Keycloak`);
    const authInfo = await verifyAccessToken(token);
    globalTokenCache.set(token, authInfo);
    console.log(`✅ [TokenCache] Token validated and cached`);
    return authInfo;
  };

  // Client information provider using shared utility
  const getClient = createOAuth2ClientProvider(config);

  // Create the OAuth2 provider
  const oauthProvider = new ProxyOAuthServerProvider({
    endpoints: {
      authorizationUrl: config.keycloak.authUrl,
      tokenUrl: config.keycloak.tokenUrl,
      revocationUrl: config.keycloak.revocationUrl,
      registrationUrl: config.keycloak.registrationUrl,
    },
    verifyAccessToken: cachedVerifyAccessToken,
    getClient,
    fetch: fetch,
  });

  // Mount the MCP auth router (handles OAuth2 endpoints)
  app.use(
    mcpAuthRouter({
      provider: oauthProvider,
      issuerUrl: new URL(config.keycloak.issuer),
      baseUrl: new URL(config.server.baseUrl),
    })
  );

  // Create bearer auth middleware for protecting MCP endpoints
  const bearerAuthMiddleware = requireBearerAuth({
    verifier: { verifyAccessToken: cachedVerifyAccessToken },
    requiredScopes: [], // No specific scopes required
    resourceMetadataUrl: `${config.server.baseUrl}/.well-known/oauth-protected-resource`,
  });

  console.error('✅ MCP OAuth2 authentication setup complete');
  console.error('🔍 OAuth2 endpoints will be handled by mcpAuthRouter');
  console.error('🔐 Bearer auth middleware created for MCP endpoint protection');

  return {
    oauthProvider,
    bearerAuthMiddleware,
  };
}
