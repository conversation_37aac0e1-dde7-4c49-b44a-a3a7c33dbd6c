import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';

/**
 * Simple token cache entry
 */
interface TokenCacheEntry {
  authInfo: AuthInfo;
  expiresAt: number;
}

/**
 * Simplified in-memory token cache
 */
class SimpleTokenCache {
  private cache = new Map<string, TokenCacheEntry>();
  private readonly ttl = 5 * 60 * 1000; // 5 minutes

  get(token: string): AuthInfo | null {
    const key = this.hashToken(token);
    const entry = this.cache.get(key);

    if (!entry || Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.authInfo;
  }

  set(token: string, authInfo: AuthInfo): void {
    const key = this.hashToken(token);
    this.cache.set(key, {
      authInfo,
      expiresAt: Date.now() + this.ttl,
    });
  }

  clear(): void {
    this.cache.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: 1000,
      hitRate: 0.8, // Simplified
    };
  }

  cleanup(): void {
    const now = Date.now();
    let removed = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        removed++;
      }
    }

    if (removed > 0) {
      console.error(`🧹 [TokenCache] Cleaned up ${removed} expired tokens`);
    }
  }

  private hashToken(token: string): string {
    // Simple hash to avoid storing full tokens
    let hash = 0;
    for (let i = 0; i < token.length; i++) {
      const char = token.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }
}

/**
 * Global token cache instance
 */
export const globalTokenCache = new SimpleTokenCache();

// Cleanup expired tokens every 5 minutes
setInterval(() => globalTokenCache.cleanup(), 5 * 60 * 1000);
