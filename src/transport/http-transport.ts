import express from 'express';
import { randomUUID } from 'node:crypto';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { OAuth2Config, getOAuth2Config } from '../auth/oauth-provider.js';
import { globalTokenCache } from '../auth/jwt-cache.js';
// Removed middleware.js - using simplified auth
import { createAuthMiddleware, getAuthStatus, setupMCPAuth } from '../auth/auth-middleware.js';
import { createRequestContextMiddleware } from '../auth/auth-context.js';

export class HttpMCPTransport {
  private app: express.Application;
  private server: any;
  private port: number;
  private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};
  private mcpServer: Server | null = null;
  private oauth2Provider: ProxyOAuthServerProvider | null = null;
  private oauth2Config: OAuth2Config | null = null;

  constructor(port: number = 8080, oauth2Provider?: ProxyOAuthServerProvider) {
    this.port = port;
    this.oauth2Provider = oauth2Provider || null;
    // Always check OAuth2 config if enabled, regardless of provider
    const config = getOAuth2Config();
    this.oauth2Config = config && config.enabled ? config : null;
    this.app = express();

    // Setup OAuth2 provider BEFORE middleware to ensure it's available for auth middleware
    this.setupMCPAuth();

    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS');
      res.header(
        'Access-Control-Allow-Headers',
        'Origin, X-Requested-With, Content-Type, Accept, Authorization, mcp-session-id, mcp-protocol-version'
      );
      res.header('Access-Control-Expose-Headers', 'mcp-session-id, mcp-protocol-version');
      if (req.method === 'OPTIONS') return res.sendStatus(200);
      next();
    });

    // Request logging middleware
    this.app.use((req, _res, next) => {
      const sessionId = req.headers['mcp-session-id'];
      console.error(
        `🌐 ${req.method} ${req.path} ${sessionId ? `[${sessionId}]` : '[no-session]'}`
      );
      next();
    });

    // Add OAuth2 discovery endpoints BEFORE authentication middleware
    // These endpoints must be publicly accessible per OAuth2 specification
    this.setupPublicOAuth2DiscoveryEndpoints();

    // Unified authentication middleware (MCP compliance)
    this.app.use(createAuthMiddleware(this.oauth2Provider || undefined));

    // Request context middleware (for auth info propagation)
    this.app.use(createRequestContextMiddleware());
  }

  /**
   * Setup OAuth2 discovery endpoints required by MCP OAuth2.0 specification
   *
   * MCP Requirements:
   * 1. OAuth2 Protected Resource Metadata (RFC9728) - MUST implement
   * 2. OAuth2 Authorization Server Metadata (RFC8414) - MUST support
   * 3. OpenID Connect Discovery 1.0 - MUST support
   * 4. WWW-Authenticate headers with resource metadata URL on 401 responses
   */
  private setupPublicOAuth2DiscoveryEndpoints() {
    // Note: All OAuth2 discovery endpoints (Protected Resource, Authorization Server, OpenID Connect)
    // are now registered in setupCustomOAuth2DiscoveryEndpoints() after MCP SDK setup to ensure
    // proper precedence and complete 3-tier discovery strategy following MCP SDK patterns
  }

  private setupRoutes() {
    this.app.post('/mcp', (req, res) => this.handleMCPPost(req, res));
    this.app.get('/mcp', (req, res) => this.handleMCPGet(req, res));
    this.app.delete('/mcp', (req, res) => this.handleSessionRequest(req, res));

    // Health check endpoints (both /health and /mcp/health for compatibility)
    const healthResponse = (_: any, res: any) =>
      res.json({
        status: 'healthy',
        transport: 'streamable-http',
        timestamp: new Date().toISOString(),
        activeSessions: Object.keys(this.transports).length,
        sessions: Object.keys(this.transports),
      });

    this.app.get('/health', healthResponse);
    this.app.get('/mcp/health', healthResponse);

    // Auth status endpoint
    this.app.get('/auth/status', (_, res) => {
      res.json(getAuthStatus());
    });

    // Debug config endpoint (non-production only)
    this.app.get('/debug/config', async (_, res) => {
      const nodeEnv = process.env.NODE_ENV?.toLowerCase();

      // Only allow in non-production environments
      if (nodeEnv === 'production' || nodeEnv === 'prod') {
        return res.status(404).json({ error: 'Not found' });
      }

      try {
        const { getGlobalConfig } = await import('../config/global-config.js');
        const { loadAuthConfig } = await import('../auth/auth-middleware.js');

        // Get the actual merged configuration (NACOS + environment + defaults)
        const globalConfig = getGlobalConfig();
        const authConfig = loadAuthConfig();

        // Get safe environment variables (no credentials) for comparison
        const safeEnvVars = this.getSafeEnvironmentVariables();

        res.json({
          timestamp: new Date().toISOString(),
          configurationHierarchy: 'NACOS > Helm > process.env > defaults',
          mergedConfig: {
            // Infrastructure
            NODE_ENV: globalConfig.NODE_ENV,
            TRANSPORT: globalConfig.TRANSPORT,
            MCP_SERVER_PORT: globalConfig.MCP_SERVER_PORT,
            MCP_SERVER_BASE_URL: globalConfig.MCP_SERVER_BASE_URL,
            MCP_SERVER_PUBLIC_BASE_URL: globalConfig.MCP_SERVER_PUBLIC_BASE_URL,

            // API Configuration
            VITE_API_HOST: globalConfig.VITE_API_HOST,
            VITE_API_HOST_KONG: globalConfig.VITE_API_HOST_KONG,
            VITE_MASTER_DATA_API_HOST: globalConfig.VITE_MASTER_DATA_API_HOST,
            VITE_MASTER_DATA_API_KEY: globalConfig.VITE_MASTER_DATA_API_KEY
              ? '***HIDDEN***'
              : undefined,

            // OAuth2 Configuration
            OAUTH2_ENABLED: globalConfig.OAUTH2_ENABLED,
            KEYCLOAK_BASE_URL: globalConfig.KEYCLOAK_BASE_URL,
            KEYCLOAK_REALM: globalConfig.KEYCLOAK_REALM,
            // Note: OAUTH2_CLIENT_ID is now provided by clients in requests
            OAUTH2_CLIENT_SECRET: globalConfig.OAUTH2_CLIENT_SECRET ? '***HIDDEN***' : undefined,

            // Authentication
            AUTH_COOKIES: globalConfig.AUTH_COOKIES ? '***HIDDEN***' : undefined,
            CONNECTION_AUTH_ENABLED: globalConfig.CONNECTION_AUTH_ENABLED,
            CONNECTION_AUTH_STRICT: globalConfig.CONNECTION_AUTH_STRICT,
            PER_REQUEST_AUTH_ENABLED: globalConfig.PER_REQUEST_AUTH_ENABLED,
          },
          authConfig: {
            connectionAuthEnabled: authConfig.connectionAuthEnabled,
            connectionAuthStrict: authConfig.connectionAuthStrict,
            connectionAuthTestApi: authConfig.connectionAuthTestApi,
            perRequestAuthEnabled: authConfig.perRequestAuthEnabled,
            cacheEnabled: authConfig.cacheEnabled,
            cacheMaxAge: authConfig.cacheMaxAge,
            logValidation: authConfig.logValidation,
          },
          rawEnvironmentVariables: {
            note: 'These are raw environment variables (for comparison with merged config above)',
            ...safeEnvVars,
          },
        });
      } catch (error) {
        res.status(500).json({
          error: 'Failed to load configuration',
          message: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });

    this.app.get('/mcp/capabilities', (_, res) => {
      const capabilities = {
        transport: 'streamable-http',
        streaming: true,
        protocols: ['jsonrpc-2.0'],
        version: '1.0.0',
        features: ['session-management', 'server-sent-events'],
        oauth2_enabled: !!(this.oauth2Provider && this.oauth2Config),
        ...(this.oauth2Config && {
          oauth2: {
            protected_resource_url: `${this.oauth2Config.server.baseUrl}/.well-known/oauth-protected-resource`,
            authorization_server_url: `${this.oauth2Config.keycloak.issuer}/.well-known/oauth-authorization-server`,
            scopes_supported: ['openid', 'profile', 'email'], // Default scopes
            resource_documentation: 'https://modelcontextprotocol.io/',
          },
        }),
      };

      res.json(capabilities);
    });

    // OAuth2 setup is now done in constructor before middleware setup
  }

  private setupMCPAuth() {
    if (!this.oauth2Config) return;

    console.error('🔧 Setting up MCP-compliant OAuth2 authentication...');

    // Use the new MCP-compliant auth setup
    const { oauthProvider } = setupMCPAuth(this.app, this.oauth2Config);

    // Store the provider for later use
    this.oauth2Provider = oauthProvider;

    // Note: Bearer auth middleware is handled by createAuthMiddleware in setupMiddleware()
    // No need to apply it again here to avoid conflicts

    console.error('✅ MCP OAuth2 authentication setup complete');
    console.error('🔍 OAuth2 endpoints will be handled by mcpAuthRouter');
    console.error('🔐 MCP endpoint authentication handled by createAuthMiddleware');

    // Override MCP SDK OAuth2 discovery endpoints with our custom implementations
    this.setupCustomOAuth2DiscoveryEndpoints();

    // Protected cache endpoints with scope-based authorization
    // Token cache status endpoint (simplified)
    this.app.get('/auth/cache/status', (_, res) => {
      const stats = globalTokenCache.getStats();
      res.json({
        cache: stats,
        timestamp: new Date().toISOString(),
      });
    });

    // Simplified cache management (no auth required for now)
    this.app.post('/auth/cache/clear', (_, res) => {
      globalTokenCache.clear();
      res.json({
        message: 'Token cache cleared successfully',
        timestamp: new Date().toISOString(),
      });
    });

    // Removed OAuth2 logging endpoints - using simplified auth

    // OAuth2 endpoints and discovery are now handled by mcpAuthRouter in setupMCPAuth()
    console.error('🔧 OAuth2 Server Configuration:');
    console.error(
      `   Server Auth Secret: ${this.oauth2Config.serverAuth?.secret ? '***SET***' : '***NOT SET***'}`
    );
    console.error(`   Server Auth Method: ${this.oauth2Config.serverAuth?.authMethod}`);
    console.error('   Note: client_id, redirect_uri, and scopes provided by clients in requests');
    console.error('✅ MCP Inspector will use dynamic client registration');
  }

  setMCPServer(server: Server) {
    this.mcpServer = server;
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, '0.0.0.0', () => {
        console.error(`🚀 HTTP MCP Transport on port ${this.port}`);
        if (this.oauth2Provider) console.error(`🔐 OAuth2 enabled`);
        resolve();
      });
      this.server.on('error', reject);
    });
  }

  async stop(): Promise<void> {
    if (this.server) {
      return new Promise(resolve => this.server.close(resolve));
    }
  }

  /**
   * Helper function to send JSON-RPC error responses
   */
  private sendJsonRpcError(
    res: express.Response,
    code: number,
    message: string,
    data?: any,
    id: any = null
  ) {
    if (!res.headersSent) {
      res.status(code >= -32000 && code <= -32099 ? 400 : 500).json({
        jsonrpc: '2.0',
        error: { code, message, ...(data && { data }) },
        id,
      });
    }
  }

  /**
   * Helper function to handle authentication for MCP requests
   */
  private async handleAuthentication(
    req: express.Request,
    res: express.Response,
    context: string
  ): Promise<boolean> {
    const { loadAuthConfig, authenticate } = await import('../auth/auth-middleware.js');
    const authConfig = loadAuthConfig();

    const shouldAuthenticate =
      context === 'connection'
        ? authConfig.connectionAuthEnabled
        : authConfig.connectionAuthEnabled || authConfig.perRequestAuthEnabled;

    if (!shouldAuthenticate) {
      console.log(`🔓 [HttpTransport] ${context} authentication disabled, skipping auth check`);
      return true;
    }

    const authResult = await authenticate(req, this.oauth2Provider || undefined);

    if (!authResult.success) {
      console.log(`❌ [HttpTransport] ${context} authentication failed:`, authResult.error);

      const errorType = authResult.error?.includes('Missing required scopes')
        ? 'insufficient_scope'
        : 'invalid_token';

      // Simple error response
      res.status(401).json({
        error: errorType,
        error_description: authResult.error || 'Authentication failed',
      });
      return false;
    }

    console.log(`✅ [HttpTransport] ${context} authentication successful:`, {
      clientId: authResult.authInfo?.clientId,
      cached: authResult.cached,
    });
    return true;
  }

  private async handleMCPPost(req: express.Request, res: express.Response) {
    try {
      const sessionId = req.headers['mcp-session-id'] as string | undefined;

      let transport: StreamableHTTPServerTransport;

      if (sessionId && this.transports[sessionId]) {
        transport = this.transports[sessionId];
      } else if (
        !sessionId &&
        (isInitializeRequest(req.body) || req.body?.method === 'initialize')
      ) {
        // Authenticate connection establishment
        const authSuccess = await this.handleAuthentication(req, res, 'connection');
        if (!authSuccess) return;

        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: sessionId => {
            this.transports[sessionId] = transport;
          },
          enableDnsRebindingProtection: false,
        });

        transport.onclose = () => {
          if (transport.sessionId) {
            delete this.transports[transport.sessionId];
          }
        };

        if (!this.mcpServer) throw new Error('MCP server not initialized');
        await this.mcpServer.connect(transport);
      } else {
        return this.sendJsonRpcError(res, -32000, 'Bad Request: No valid session ID provided');
      }

      await transport.handleRequest(req, res, req.body);
    } catch (error) {
      this.sendJsonRpcError(
        res,
        -32603,
        'Internal error',
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  private async handleMCPGet(req: express.Request, res: express.Response) {
    try {
      // Authenticate GET requests
      const authSuccess = await this.handleAuthentication(req, res, 'GET');
      if (!authSuccess) return;

      // After successful authentication, handle as session request
      return this.handleSessionRequest(req, res);
    } catch (error) {
      console.error('❌ [HttpTransport] GET request error:', error);
      if (!res.headersSent) {
        res.status(500).send('Internal server error');
      }
    }
  }

  private async handleSessionRequest(req: express.Request, res: express.Response) {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;

    if (!sessionId || !this.transports[sessionId]) {
      return res.status(400).send('Invalid or missing session ID');
    }

    try {
      await this.transports[sessionId].handleRequest(req, res);
    } catch {
      if (!res.headersSent) {
        res.status(500).send('Internal server error');
      }
    }
  }

  /**
   * Get safe environment variables (excluding credentials and secrets)
   */
  private getSafeEnvironmentVariables(): Record<string, string | undefined> {
    const sensitiveKeys = [
      'AUTH_COOKIES',
      'OAUTH2_CLIENT_SECRET',
      'KEYCLOAK_CLIENT_SECRET',
      'VITE_MASTER_DATA_API_KEY',
      'NACOS_ACCESS_KEY',
      'NACOS_SECRET_KEY',
      'JWT_SECRET',
      'SESSION_SECRET',
      'API_KEY',
      'SECRET',
      'PASSWORD',
      'TOKEN',
      'PRIVATE_KEY',
    ];

    const safeVars: Record<string, string | undefined> = {};

    Object.keys(process.env).forEach(key => {
      const isSensitive = sensitiveKeys.some(sensitiveKey =>
        key.toUpperCase().includes(sensitiveKey.toUpperCase())
      );

      if (!isSensitive) {
        safeVars[key] = process.env[key];
      } else {
        safeVars[key] = process.env[key] ? '***HIDDEN***' : undefined;
      }
    });

    return safeVars;
  }

  async close(): Promise<void> {
    // Close all active transports
    Object.values(this.transports).forEach(transport => {
      try {
        transport.close();
      } catch (error) {
        console.error('Error closing transport:', error);
      }
    });
    this.transports = {};

    // Close the HTTP server
    if (this.server) {
      return new Promise(resolve => this.server.close(resolve));
    }
  }

  getActiveSessions(): string[] {
    return Object.keys(this.transports);
  }

  getSessionCount(): number {
    return Object.keys(this.transports).length;
  }

  getPort(): number {
    return this.port;
  }

  /**
   * Setup complete OAuth2 discovery strategy following MCP SDK patterns
   * Implements 3-tier discovery: MCP endpoint, context path, and domain root
   * This method is called after setupMCPAuth to ensure our endpoints take precedence
   */
  private setupCustomOAuth2DiscoveryEndpoints() {
    if (!this.oauth2Config) return;

    console.error('🔧 Setting up complete OAuth2 discovery strategy...');

    // Helper function to create OAuth2 discovery response with CORS
    const createOAuth2DiscoveryHandler = (logContext: string) => {
      return (req: express.Request, res: express.Response) => {
        // Enable CORS for web-based MCP clients (following SDK pattern)
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET');
        res.header('Access-Control-Allow-Headers', 'Content-Type, mcp-protocol-version');
        res.header('Access-Control-Expose-Headers', 'mcp-session-id, mcp-protocol-version');

        // Only allow GET method (following SDK pattern)
        if (req.method !== 'GET') {
          return res.status(405).json({ error: 'Method not allowed' });
        }

        console.error(`🔍 [${logContext}] Serving OAuth2 discovery endpoint`);

        const response = {
          resource: `${this.oauth2Config!.server.baseUrl}/mcp`,
          authorization_servers: [this.oauth2Config!.server.baseUrl], // Point to ourselves as authorization server proxy
          scopes_supported: ['openid', 'profile', 'email'], // Default scopes
          resource_documentation: 'https://modelcontextprotocol.io/',
        };

        res.json(response);
      };
    };

    const clientId = 'mcp-server-odi'; // Default client ID for discovery endpoints

    // Strategy 1: MCP Endpoint Discovery (most specific)
    // Pattern: /mcp-server-odi/mcp/.well-known/oauth-protected-resource
    this.app.all(
      `/${clientId}/mcp/.well-known/oauth-protected-resource`,
      createOAuth2DiscoveryHandler('MCP-ENDPOINT')
    );

    // Static version for backward compatibility
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/mcp/.well-known/oauth-protected-resource',
        createOAuth2DiscoveryHandler('MCP-ENDPOINT-STATIC')
      );
    }

    // Strategy 2: Context Path Discovery (service context aware)
    // Pattern: /mcp-server-odi/.well-known/oauth-protected-resource
    this.app.all(
      `/${clientId}/.well-known/oauth-protected-resource`,
      createOAuth2DiscoveryHandler('CONTEXT-PATH')
    );

    // Static version for backward compatibility
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/.well-known/oauth-protected-resource',
        createOAuth2DiscoveryHandler('CONTEXT-PATH-STATIC')
      );
    }

    // Strategy 3: Domain Root Discovery (legacy fallback)
    // Pattern: /.well-known/oauth-protected-resource
    // Use app.all() to ensure our handler takes precedence over MCP SDK
    this.app.all(
      '/.well-known/oauth-protected-resource',
      createOAuth2DiscoveryHandler('DOMAIN-ROOT')
    );

    // Additional MCP-specific endpoints
    this.app.all(
      '/mcp/.well-known/oauth-protected-resource',
      createOAuth2DiscoveryHandler('MCP-NAMESPACE')
    );
    this.app.all(
      '/.well-known/oauth-protected-resource/mcp',
      createOAuth2DiscoveryHandler('MCP-PATH')
    );

    // Ingress-specific endpoints (for load balancer routing)
    this.app.all(
      `/.well-known/oauth-protected-resource/${clientId}/mcp`,
      createOAuth2DiscoveryHandler('INGRESS-DYNAMIC')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/.well-known/oauth-protected-resource/mcp-server-odi/mcp',
        createOAuth2DiscoveryHandler('INGRESS-STATIC')
      );
    }

    // Setup Authorization Server Discovery endpoints
    this.setupAuthorizationServerDiscoveryEndpoints(clientId);

    console.error('✅ Complete OAuth2 discovery strategy implemented');
    console.error(
      `🔍 Discovery endpoints return MCP resource: ${this.oauth2Config.server.baseUrl}/mcp`
    );
    console.error(`🔍 Client ID: ${clientId}`);
  }

  /**
   * Setup Authorization Server discovery endpoints following the same 3-tier strategy
   */
  private setupAuthorizationServerDiscoveryEndpoints(clientId: string) {
    if (!this.oauth2Config) return;

    // Helper function to create Authorization Server metadata handler
    const createAuthServerHandler = (logContext: string) => {
      return async (req: express.Request, res: express.Response) => {
        // Enable CORS for web-based MCP clients
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET');
        res.header('Access-Control-Allow-Headers', 'Content-Type, mcp-protocol-version');
        res.header('Access-Control-Expose-Headers', 'mcp-session-id, mcp-protocol-version');

        // Only allow GET method
        if (req.method !== 'GET') {
          return res.status(405).json({ error: 'Method not allowed' });
        }

        console.error(`🔍 [${logContext}] Serving OAuth2 Authorization Server Metadata`);

        try {
          // Try OAuth2 Authorization Server Metadata first (RFC8414)
          const oauth2Response = await fetch(
            `${this.oauth2Config!.keycloak.issuer}/.well-known/oauth-authorization-server`
          );
          if (oauth2Response.ok) {
            const oauth2Config = await oauth2Response.json();
            return res.json(oauth2Config);
          }
        } catch {
          console.error(
            `⚠️ [${logContext}] OAuth2 Authorization Server Metadata not available, falling back to OpenID Connect`
          );
        }

        // Fallback to OpenID Connect Discovery
        try {
          const oidcResponse = await fetch(
            `${this.oauth2Config!.keycloak.issuer}/.well-known/openid-configuration`
          );
          if (oidcResponse.ok) {
            const oidcConfig = await oidcResponse.json();
            return res.json(oidcConfig);
          }
          throw new Error(`Failed to fetch OpenID configuration: ${oidcResponse.status}`);
        } catch (error) {
          console.error(`❌ [${logContext}] Failed to fetch authorization server metadata:`, error);
          res.status(500).json({
            error: 'Failed to fetch authorization server metadata',
            details: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      };
    };

    // Strategy 1: MCP Endpoint Discovery
    this.app.all(
      `/${clientId}/mcp/.well-known/oauth-authorization-server`,
      createAuthServerHandler('AUTH-MCP-ENDPOINT')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/mcp/.well-known/oauth-authorization-server',
        createAuthServerHandler('AUTH-MCP-ENDPOINT-STATIC')
      );
    }

    // Strategy 2: Context Path Discovery
    this.app.all(
      `/${clientId}/.well-known/oauth-authorization-server`,
      createAuthServerHandler('AUTH-CONTEXT-PATH')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/.well-known/oauth-authorization-server',
        createAuthServerHandler('AUTH-CONTEXT-PATH-STATIC')
      );
    }

    // Strategy 3: Domain Root Discovery
    this.app.all(
      '/.well-known/oauth-authorization-server',
      createAuthServerHandler('AUTH-DOMAIN-ROOT')
    );

    // Ingress-specific endpoints
    this.app.all(
      `/.well-known/oauth-authorization-server/${clientId}/mcp`,
      createAuthServerHandler('AUTH-INGRESS-DYNAMIC')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/.well-known/oauth-authorization-server/mcp-server-odi/mcp',
        createAuthServerHandler('AUTH-INGRESS-STATIC')
      );
    }

    console.error('✅ Authorization Server discovery endpoints registered');

    // Setup OpenID Connect Discovery endpoints
    this.setupOpenIdConnectDiscoveryEndpoints(clientId);
  }

  /**
   * Setup OpenID Connect discovery endpoints following the same 3-tier strategy
   */
  private setupOpenIdConnectDiscoveryEndpoints(clientId: string) {
    if (!this.oauth2Config) return;

    // Helper function to create OpenID Connect discovery handler
    const createOidcHandler = (logContext: string) => {
      return async (req: express.Request, res: express.Response) => {
        // Enable CORS for web-based MCP clients
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET');
        res.header('Access-Control-Allow-Headers', 'Content-Type, mcp-protocol-version');
        res.header('Access-Control-Expose-Headers', 'mcp-session-id, mcp-protocol-version');

        // Only allow GET method
        if (req.method !== 'GET') {
          return res.status(405).json({ error: 'Method not allowed' });
        }

        console.error(`🔍 [${logContext}] Proxying OpenID configuration from Keycloak`);

        try {
          const oidcResponse = await fetch(
            `${this.oauth2Config!.keycloak.issuer}/.well-known/openid-configuration`
          );
          if (oidcResponse.ok) {
            const oidcConfig = await oidcResponse.json();
            return res.json(oidcConfig);
          }
          throw new Error(`Failed to fetch OpenID configuration: ${oidcResponse.status}`);
        } catch (error) {
          console.error(`❌ [${logContext}] Failed to fetch OpenID configuration:`, error);
          res.status(500).json({
            error: 'Failed to fetch OpenID configuration',
            details: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      };
    };

    // Strategy 1: MCP Endpoint Discovery
    this.app.all(
      `/${clientId}/mcp/.well-known/openid-configuration`,
      createOidcHandler('OIDC-MCP-ENDPOINT')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/mcp/.well-known/openid-configuration',
        createOidcHandler('OIDC-MCP-ENDPOINT-STATIC')
      );
    }

    // Strategy 2: Context Path Discovery
    this.app.all(
      `/${clientId}/.well-known/openid-configuration`,
      createOidcHandler('OIDC-CONTEXT-PATH')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/mcp-server-odi/.well-known/openid-configuration',
        createOidcHandler('OIDC-CONTEXT-PATH-STATIC')
      );
    }

    // Strategy 3: Domain Root Discovery
    this.app.all('/.well-known/openid-configuration', createOidcHandler('OIDC-DOMAIN-ROOT'));

    // MCP-specific endpoints
    this.app.all('/mcp/.well-known/openid-configuration', createOidcHandler('OIDC-MCP-NAMESPACE'));

    // Ingress-specific endpoints
    this.app.all(
      `/.well-known/openid-configuration/${clientId}/mcp`,
      createOidcHandler('OIDC-INGRESS-DYNAMIC')
    );
    if (clientId !== 'mcp-server-odi') {
      this.app.all(
        '/.well-known/openid-configuration/mcp-server-odi/mcp',
        createOidcHandler('OIDC-INGRESS-STATIC')
      );
    }

    console.error('✅ OpenID Connect discovery endpoints registered');
  }
}
