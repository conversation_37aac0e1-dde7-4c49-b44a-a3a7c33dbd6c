#!/usr/bin/env node
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { createServer, createMcpServer } from './server.js';

import { initializeGlobalConfig, getGlobalConfig } from './config/global-config.js';
import { logEnvironmentInfoWithNacos, validateEnvironmentWithNacos } from './utils/env.js';
import { HttpMCPTransport } from './transport/http-transport.js';

async function main() {
  // Initialize global configuration with NACOS integration first
  console.error('📋 Initializing global configuration (including NACOS)...');
  await initializeGlobalConfig();

  // Get configuration from global store
  const env = getGlobalConfig();

  // Log environment information (now with NACOS data)
  logEnvironmentInfoWithNacos(env);

  // Validate environment (now with NACOS data)
  const envValidation = validateEnvironmentWithNacos(env);

  // Show warnings and security issues
  if (envValidation.warnings.length > 0) {
    console.error('⚠️  Environment warnings:');
    envValidation.warnings.forEach((warning: string) => console.error(`  - ${warning}`));
  }

  if (envValidation.security.length > 0) {
    console.error('🔒 Security recommendations:');
    envValidation.security.forEach((security: string) => console.error(`  - ${security}`));
  }

  // Exit on missing required variables
  if (!envValidation.valid) {
    console.error('❌ Environment validation failed - missing required variables:');
    envValidation.missing.forEach((missing: string) => console.error(`  - ${missing}`));
    console.error('💡 Tip: Check NACOS configuration or set environment variables');
    process.exit(1);
  }

  // Create enhanced MCP server with full capabilities
  const mcpServer = createMcpServer();

  // Get the underlying server for transport compatibility
  const server = mcpServer.server;

  // Determine transport type
  const transportType = process.env.TRANSPORT || 'stdio';

  if (transportType === 'http') {
    // HTTP transport mode with official StreamableHTTPServerTransport
    console.error('🌐 Starting Streamable HTTP transport mode...');
    const httpTransport = new HttpMCPTransport(env.MCP_SERVER_PORT);

    // Set the MCP server instance for the transport
    httpTransport.setMCPServer(server);

    // Start the HTTP server
    await httpTransport.start();

    console.error(
      `✅ orders-portal-mcp-server running on Streamable HTTP transport (port ${env.MCP_SERVER_PORT})`
    );
    console.error(`📡 Session-based MCP communication with Server-Sent Events`);

    if (env.OAUTH2_ENABLED) {
      console.error(`🔐 OAuth2 endpoints integrated with HTTP transport`);
      console.error(
        `📋 OAuth2 metadata: ${env.MCP_SERVER_BASE_URL}/.well-known/oauth-protected-resource`
      );
    }
  } else {
    // Default stdio transport mode
    console.error('📡 Starting stdio transport mode...');
    const transport = new StdioServerTransport();
    await mcpServer.connect(transport);
    console.error('✅ orders-portal-mcp-server running on stdio with enhanced capabilities');

    if (env.OAUTH2_ENABLED) {
      console.error(`🔐 OAuth2 HTTP server available at: ${env.MCP_SERVER_BASE_URL}`);
      console.error(
        `📋 OAuth2 metadata: ${env.MCP_SERVER_BASE_URL}/.well-known/oauth-authorization-server`
      );
    }
  }
}

main().catch(error => {
  console.error('Fatal error in main():', error);
  process.exit(1);
});
