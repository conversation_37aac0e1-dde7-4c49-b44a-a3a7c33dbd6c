import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ToolSchema,
  SetLevelRequestSchema,
  EmptyResultSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { SERVICE_MODULES, SERVICE_TOOL_CONFIGS } from './adapters/service-adapter.js';

// type ToolInput = z.infer<typeof ToolSchema.shape.inputSchema>;

// Safe JSON stringify function to handle circular references
function safeStringify(obj: any, indent: number = 0): string {
  const seen = new WeakSet();

  const replacer = (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }

    // Handle special object types
    if (value instanceof Error) {
      return {
        name: value.name,
        message: value.message,
        stack: value.stack,
      };
    }

    // Handle functions
    if (typeof value === 'function') {
      return '[Function]';
    }

    // Handle undefined
    if (value === undefined) {
      return '[Undefined]';
    }

    // Handle HTTP request/response objects
    if (value && typeof value === 'object') {
      // Skip problematic HTTP-related properties
      if (
        value.constructor &&
        (value.constructor.name === 'ClientRequest' ||
          value.constructor.name === 'IncomingMessage' ||
          value.constructor.name === 'Agent' ||
          value.constructor.name === 'TLSSocket')
      ) {
        return `[${value.constructor.name}]`;
      }
    }

    return value;
  };

  try {
    return JSON.stringify(obj, replacer, indent);
  } catch {
    // If still fails, return a safe string representation
    return `[Object: ${typeof obj}]`;
  }
}

// 所有业务逻辑现在都在 service-adapter.ts 中实现

/**
 * 创建增强版 MCP 服务器，支持完整的 MCP 能力
 * 包括 Tools, Resources, Prompts, Logging, Completions
 */
export function createMcpServer(): McpServer {
  const mcpServer = new McpServer({
    name: 'mcp-server-odi',
    version: '0.1.0',
  }, {
    capabilities: {
      tools: { listChanged: true },
      resources: { subscribe: true, listChanged: true },
      prompts: { listChanged: true },
      completions: {},
      logging: {}
    }
  });

  // 注册所有工具
  registerTools(mcpServer);

  // 注册资源
  registerResources(mcpServer);

  // 注册提示模板
  registerPrompts(mcpServer);

  // 注册logging处理器
  registerLogging(mcpServer);

  return mcpServer;
}

/**
 * 向后兼容的服务器创建函数
 * 保持现有的 Server 实现以确保向后兼容性
 */
export function createServer(): Server {
  const server = new Server(
    {
      name: 'mcp-server-odi',
      version: '0.1.0',
    },
    {
      capabilities: {
        tools: {},
        logging: {},
      },
    }
  );

  // List tools handler - 使用 service-adapter 的配置
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    const tools: any[] = [];

    Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
      functions.forEach(func => {
        const toolName = `${moduleName}_${func.name}`;

        const toolDef: any = {
          name: toolName,
          description: func.description,
        };

        // 如果有 Zod schema，转换为 JSON schema
        if ((func as any).zodSchema) {
          toolDef.inputSchema = zodToJsonSchema((func as any).zodSchema);
        }

        // Add elicitation metadata for client handling
        if ((func as any).capability) {
          toolDef.capability = (func as any).capability;
        }
        if ((func as any).riskLevel) {
          toolDef.riskLevel = (func as any).riskLevel;
        }
        if ((func as any).category) {
          toolDef.category = (func as any).category;
        }
        if ((func as any).requiresConfirmation !== undefined) {
          toolDef.requiresConfirmation = (func as any).requiresConfirmation;
        }
        if ((func as any).tags) {
          toolDef.tags = (func as any).tags;
        }

        tools.push(toolDef);
      });
    });

    return { tools };
  });

  // Call tool handler - 使用 service-adapter 的通用处理器
  server.setRequestHandler(CallToolRequestSchema, async request => {
    const { name, arguments: args } = request.params;

    try {
      // 解析工具名称
      const [moduleName, functionName] = name.split('_', 2);

      // 获取服务模块
      const serviceModule = (SERVICE_MODULES as any)[moduleName];
      if (!serviceModule) {
        throw new Error(`Service module '${moduleName}' not found`);
      }

      // 获取服务函数 (now from functions property)
      const serviceFunction = serviceModule.functions?.[functionName];
      if (!serviceFunction) {
        throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);
      }

      // 获取对应的 Zod schema 进行验证
      const moduleConfig = (SERVICE_TOOL_CONFIGS as any)[moduleName];
      const functionConfig = moduleConfig?.find((f: any) => f.name === functionName);

      let validatedArgs = args;
      if (functionConfig?.zodSchema) {
        validatedArgs = functionConfig.zodSchema.parse(args);
      }

      // 调用服务函数 (auth context is now handled via request context)
      const result = await serviceFunction(validatedArgs);

      return {
        content: [
          {
            type: 'text',
            text: safeStringify(result, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  });

  // 处理 logging/setLevel 请求
  server.setRequestHandler(SetLevelRequestSchema, async (request) => {
    const { level } = request.params;

    // 更新环境变量中的日志级别
    process.env.LOG_LEVEL = level.toUpperCase();

    // 记录日志级别变更
    console.error(`🔧 Log level changed to: ${level.toUpperCase()}`);

    // 返回空结果（符合MCP协议）
    return {};
  });

  return server;
}

/**
 * 注册所有工具到 McpServer
 * 使用结构化输出来改善 AI 的理解能力
 */
function registerTools(mcpServer: McpServer): void {
  Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
    functions.forEach(func => {
      const toolName = `${moduleName}_${func.name}`;

      // Extract the shape from Zod schema if it exists
      let inputSchema: any = undefined;
      if (func.zodSchema) {
        // If it's a ZodObject, extract its shape
        if (func.zodSchema._def && func.zodSchema._def.shape) {
          inputSchema = func.zodSchema._def.shape();
        } else if (func.zodSchema.shape) {
          inputSchema = func.zodSchema.shape;
        }
      }

      // 使用新的 registerTool 方法，支持 outputSchema
      mcpServer.registerTool(
        toolName,
        {
          description: func.description,
          inputSchema: inputSchema,
          // 定义结构化输出 schema
          outputSchema: {
            success: z.boolean().describe('Whether the operation was successful'),
            data: z.any().describe('The actual response data from the service'),
            metadata: z.object({
              module: z.string().describe('Service module name'),
              function: z.string().describe('Function name that was called'),
              timestamp: z.string().describe('Execution timestamp'),
              executionTime: z.number().optional().describe('Execution time in milliseconds')
            }).passthrough().describe('Metadata about the operation')
          }
        },
        async (args: any, { sendNotification }: any) => {
          const startTime = Date.now();

          // 发送开始执行日志
          await sendNotification({
            method: 'notifications/message',
            params: {
              level: 'info',
              logger: moduleName,
              data: `Executing ${func.name} with args: ${JSON.stringify(args)}`
            }
          });

          try {
            const serviceModule = (SERVICE_MODULES as any)[moduleName];
            if (!serviceModule) {
              throw new Error(`Service module '${moduleName}' not found`);
            }

            const serviceFunction = serviceModule.functions?.[func.name];
            if (!serviceFunction) {
              throw new Error(`Function '${func.name}' not found in module '${moduleName}'`);
            }

            const result = await serviceFunction(args);
            const executionTime = Date.now() - startTime;

            // 发送成功日志
            await sendNotification({
              method: 'notifications/message',
              params: {
                level: 'info',
                logger: moduleName,
                data: `Successfully executed ${func.name} in ${executionTime}ms`
              }
            });

            // 创建结构化内容
            const structuredContent = {
              success: true,
              data: cleanResponseData(result),
              metadata: {
                module: moduleName,
                function: func.name,
                timestamp: new Date().toISOString(),
                executionTime
              }
            };

            return {
              content: [{
                type: 'text',
                text: `✅ ${func.name} executed successfully\n\nData: ${JSON.stringify(structuredContent.data, null, 2)}\n\nMetadata: ${JSON.stringify(structuredContent.metadata, null, 2)}`
              }],
              structuredContent
            };
          } catch (error) {
            const executionTime = Date.now() - startTime;

            // 发送错误日志
            await sendNotification({
              method: 'notifications/message',
              params: {
                level: 'error',
                logger: moduleName,
                data: `Error executing ${func.name}: ${error instanceof Error ? error.message : String(error)}`
              }
            });

            // 即使出错也返回结构化内容
            const structuredContent = {
              success: false,
              data: null,
              metadata: {
                module: moduleName,
                function: func.name,
                timestamp: new Date().toISOString(),
                executionTime,
                error: error instanceof Error ? error.message : String(error)
              }
            };

            return {
              content: [{
                type: 'text',
                text: `❌ ${func.name} failed: ${error instanceof Error ? error.message : String(error)}`
              }],
              structuredContent
            };
          }
        }
      );
    });
  });
}

/**
 * 清理响应数据，移除不必要的信息
 * 只保留核心业务数据，去掉 headers、metadata 等
 */
function cleanResponseData(rawData: any): any {
  if (!rawData || typeof rawData !== 'object') {
    return rawData;
  }

  // 如果是标准的 HTTP 响应格式，提取 data 部分
  if (rawData.data !== undefined) {
    return cleanResponseData(rawData.data);
  }

  // 如果有 body 字段，提取 body
  if (rawData.body !== undefined) {
    return cleanResponseData(rawData.body);
  }

  // 移除常见的无用字段
  const cleanData = { ...rawData };
  const fieldsToRemove = [
    'headers', 'status', 'statusText', 'config', 'request',
    'timestamp', 'requestId', 'traceId', 'spanId'
  ];

  fieldsToRemove.forEach(field => {
    delete cleanData[field];
  });

  // 递归清理嵌套对象
  Object.keys(cleanData).forEach(key => {
    if (cleanData[key] && typeof cleanData[key] === 'object') {
      cleanData[key] = cleanResponseData(cleanData[key]);
    }
  });

  return cleanData;
}



/**
 * 注册资源到 McpServer
 */
function registerResources(mcpServer: McpServer): void {
  // 系统配置资源
  mcpServer.resource(
    'system-config',
    'config://system',
    {
      description: 'Current system configuration and environment variables',
      mimeType: 'application/json'
    },
    async (uri) => {
      const config = {
        environment: process.env.NODE_ENV || 'development',
        transport: process.env.TRANSPORT || 'stdio',
        oauth2Enabled: process.env.OAUTH2_ENABLED === 'true',
        debugMode: process.env.DEBUG_SERVICE_ADAPTER === 'true',
        timestamp: new Date().toISOString()
      };

      return {
        contents: [{
          uri: uri.toString(),
          mimeType: 'application/json',
          text: JSON.stringify(config, null, 2)
        }]
      };
    }
  );

  // 服务状态资源
  mcpServer.resource(
    'service-status',
    'status://services',
    {
      description: 'Current status of all service modules',
      mimeType: 'application/json'
    },
    async (uri) => {
      const status = {
        modules: Object.keys(SERVICE_MODULES),
        totalTools: Object.values(SERVICE_TOOL_CONFIGS).reduce((sum, tools) => sum + tools.length, 0),
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };

      return {
        contents: [{
          uri: uri.toString(),
          mimeType: 'application/json',
          text: JSON.stringify(status, null, 2)
        }]
      };
    }
  );
}

/**
 * 注册提示模板到 McpServer
 */
function registerPrompts(mcpServer: McpServer): void {
  // 简单的帮助提示
  mcpServer.prompt(
    'help',
    'Get help information about available tools and services',
    async () => {
      const toolCount = Object.values(SERVICE_TOOL_CONFIGS).reduce((sum, tools) => sum + tools.length, 0);
      const moduleList = Object.keys(SERVICE_MODULES).join(', ');

      return {
        description: 'Help information for MCP Server ODI',
        messages: [
          {
            role: 'user',
            content: {
              type: 'text',
              text: `This MCP server provides access to ${toolCount} tools across ${moduleList} modules. Use the tools/list command to see all available tools.`
            }
          }
        ]
      };
    }
  );
}

/**
 * 注册logging处理器
 */
function registerLogging(mcpServer: McpServer): void {
  // 处理 logging/setLevel 请求 - 通过底层server实例
  mcpServer.server.setRequestHandler(SetLevelRequestSchema, async (request) => {
    const { level } = request.params;

    // 更新环境变量中的日志级别
    process.env.LOG_LEVEL = level.toUpperCase();

    // 记录日志级别变更
    console.error(`🔧 Log level changed to: ${level.toUpperCase()}`);

    // 返回空结果（符合MCP协议）
    return {};
  });
}
