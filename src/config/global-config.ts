/**
 * Global Configuration Manager
 *
 * Centralized configuration store that loads NACOS + environment variables once at startup
 * and provides synchronous access throughout the application.
 */

import { EnvironmentConfig } from '../utils/env.js';
import { getEnvironmentConfig } from '../utils/env.js';

/**
 * Global configuration store
 */
class GlobalConfigManager {
  private static instance: GlobalConfigManager;
  private config: EnvironmentConfig | null = null;
  private initialized = false;
  private testMode = false; // Disable auto-initialization in tests

  private constructor() {}

  static getInstance(): GlobalConfigManager {
    if (!GlobalConfigManager.instance) {
      GlobalConfigManager.instance = new GlobalConfigManager();
    }
    return GlobalConfigManager.instance;
  }

  /**
   * Initialize configuration with NACOS integration
   * This should be called once at application startup
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.warn('⚠️ GlobalConfigManager already initialized');
      return;
    }

    console.log('📋 Initializing global configuration with NACOS...');

    try {
      this.config = await getEnvironmentConfig();
      this.initialized = true;

      console.log('✅ Global configuration initialized successfully');
      console.log(`🔍 Configuration loaded with ${Object.keys(this.config).length} properties`);

      // Log key configuration values (without secrets)
      console.log('🌍 Key Configuration:');
      console.log(`   NODE_ENV: ${this.config.NODE_ENV}`);
      console.log(`   TRANSPORT: ${this.config.TRANSPORT}`);
      console.log(`   OAUTH2_ENABLED: ${this.config.OAUTH2_ENABLED}`);
      console.log(`   PER_REQUEST_AUTH_ENABLED: ${this.config.PER_REQUEST_AUTH_ENABLED}`);
      console.log(`   CONNECTION_AUTH_ENABLED: ${this.config.CONNECTION_AUTH_ENABLED}`);
    } catch (error) {
      console.error('❌ Failed to initialize global configuration:', error);
      throw new Error(`Configuration initialization failed: ${error}`);
    }
  }

  /**
   * Get the configuration (synchronous access after initialization)
   */
  getConfig(): EnvironmentConfig {
    if (!this.initialized || !this.config) {
      // In test mode, throw error immediately without auto-initialization
      if (this.testMode) {
        throw new Error('Configuration not initialized');
      }

      console.error(
        '⚠️ [GlobalConfig] Configuration not initialized, attempting auto-initialization...'
      );
      // Try to auto-initialize synchronously for fallback scenarios
      try {
        // Use synchronous environment loading as fallback
        const { getEnvironmentConfig } = require('../utils/env.js');
        this.config = getEnvironmentConfig();
        this.initialized = true;
        console.error('✅ [GlobalConfig] Auto-initialization successful');
      } catch (error) {
        console.error('❌ [GlobalConfig] Auto-initialization failed:', error);
        throw new Error(
          'Configuration not initialized. Call GlobalConfigManager.initialize() first.'
        );
      }
    }
    return this.config!; // Non-null assertion since we just initialized it
  }

  /**
   * Get a specific configuration value
   */
  get<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] {
    return this.getConfig()[key];
  }

  /**
   * Check if configuration is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Reset configuration (for testing)
   */
  reset(): void {
    this.config = null;
    this.initialized = false;
  }

  /**
   * Enable test mode (disables auto-initialization)
   */
  enableTestMode(): void {
    this.testMode = true;
  }

  /**
   * Disable test mode (enables auto-initialization)
   */
  disableTestMode(): void {
    this.testMode = false;
  }
}

// Export singleton instance
export const globalConfig = GlobalConfigManager.getInstance();

/**
 * Convenience function to get configuration
 * Throws error if not initialized
 */
export function getGlobalConfig(): EnvironmentConfig {
  return globalConfig.getConfig();
}

/**
 * Convenience function to get a specific config value
 */
export function getConfigValue<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] {
  return globalConfig.get(key);
}

/**
 * Initialize global configuration (call this at startup)
 */
export async function initializeGlobalConfig(): Promise<void> {
  await globalConfig.initialize();
}
