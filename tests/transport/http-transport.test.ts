import { HttpMCPTransport } from '../../src/transport/http-transport.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import request from 'supertest';

// Mock dependencies
jest.mock('../../src/auth/oauth-provider.js', () => ({
  getOAuth2Config: jest.fn().mockReturnValue({
    enabled: true,
    server: { baseUrl: 'http://localhost:8080' },
    keycloak: { issuer: 'http://keycloak.test.com/realms/test' },
    client: { scopes: ['openid', 'profile'] },
  }),
}));

// Removed mcp-auth-setup.js mock - functionality moved to auth-middleware.js

jest.mock('../../src/auth/auth-middleware.js', () => ({
  createAuthMiddleware: jest.fn().mockReturnValue((_req: any, _res: any, next: any) => next()),
  getAuthStatus: jest.fn().mockReturnValue({ enabled: true, provider: 'oauth2' }),
  setupMCPAuth: jest.fn().mockReturnValue({
    oauthProvider: {
      verifyAccessToken: jest.fn().mockResolvedValue({
        clientId: 'test-client',
        scopes: ['openid'],
      }),
    },
  }),
  loadAuthConfig: jest.fn().mockReturnValue({
    connectionAuthEnabled: true,
    perRequestAuthEnabled: true,
  }),
  authenticate: jest.fn().mockResolvedValue({ success: true, authInfo: { clientId: 'test' } }),
}));

jest.mock('../../src/auth/auth-context.js', () => ({
  createRequestContextMiddleware: jest
    .fn()
    .mockReturnValue((_req: any, _res: any, next: any) => next()),
}));

jest.mock('../../src/config/global-config.js', () => ({
  getGlobalConfig: jest.fn().mockReturnValue({
    NODE_ENV: 'test',
    TRANSPORT: 'http',
    MCP_SERVER_PORT: 8080,
    OAUTH2_ENABLED: true,
  }),
}));

describe('HttpMCPTransport', () => {
  let transport: HttpMCPTransport;
  let mockServer: Server;

  beforeEach(() => {
    mockServer = {
      connect: jest.fn().mockResolvedValue(undefined),
    } as any;

    transport = new HttpMCPTransport(8080);
    transport.setMCPServer(mockServer);
  });

  afterEach(async () => {
    if (transport) {
      await transport.close();
    }
    jest.clearAllMocks();
  });

  describe('Transport Initialization', () => {
    it('should create transport with custom port when port is specified', () => {
      // Arrange: Set custom port value
      const customPort = 3000;

      // Act: Create transport with custom port
      const customTransport = new HttpMCPTransport(customPort);

      // Assert: Transport should use the specified port
      expect(customTransport.getPort()).toBe(customPort);
    });
  });

  describe('Server Lifecycle', () => {
    it('should start server successfully when valid configuration is provided', async () => {
      // Arrange: Transport is already configured in beforeEach

      // Act: Start the server
      const startPromise = transport.start();

      // Assert: Server should start without throwing errors
      await expect(startPromise).resolves.not.toThrow();
    });

    it('should handle server start errors when port is unavailable', async () => {
      // Arrange: Create transport and mock server to fail on start
      const errorTransport = new HttpMCPTransport(9999);
      const mockHttpServer = {
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === 'error') {
            setTimeout(() => handler(new Error('Port already in use')), 0);
          }
        }),
        close: jest.fn().mockImplementation(callback => callback && callback()),
      };
      errorTransport.setMCPServer(mockServer);
      errorTransport['app'].listen = jest.fn().mockReturnValue(mockHttpServer);

      // Act: Attempt to start server
      const startPromise = errorTransport.start();

      // Assert: Should throw port unavailable error
      await expect(startPromise).rejects.toThrow('Port already in use');

      // Cleanup
      await errorTransport.close();
    });
  });

  describe('Health Check', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return healthy status when server is running', async () => {
      // Arrange: Server is started in beforeEach

      // Act: Request health check endpoint
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should return healthy status with required fields
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('transport', 'streamable-http');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('activeSessions', 0);
    });
  });

  describe('Debug Configuration Access', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should hide debug config when environment is production', async () => {
      // Arrange: Set environment to production
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // Act: Request debug config endpoint
      const response = await request(transport['app']).get('/debug/config').expect(404);

      // Assert: Should return not found error
      expect(response.body).toHaveProperty('error', 'Not found');

      // Cleanup
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('MCP Capabilities', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return MCP capabilities with OAuth2 support when OAuth2 is enabled', async () => {
      // Arrange: Server is started with OAuth2 enabled (mocked in setup)

      // Act: Request MCP capabilities
      const response = await request(transport['app']).get('/mcp/capabilities').expect(200);

      // Assert: Should return capabilities including OAuth2 support
      expect(response.body).toHaveProperty('transport', 'streamable-http');
      expect(response.body).toHaveProperty('streaming', true);
      expect(response.body).toHaveProperty('protocols');
      expect(response.body.protocols).toContain('jsonrpc-2.0');
      expect(response.body).toHaveProperty('oauth2_enabled', true);
      expect(response.body.oauth2).toHaveProperty('protected_resource_url');
    });
  });

  describe('Cross-Origin Resource Sharing', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should include CORS headers when handling cross-origin requests', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a cross-origin request
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should include proper CORS headers
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toContain('GET');
      expect(response.headers['access-control-allow-methods']).toContain('POST');
    });

    it('should handle OPTIONS preflight requests correctly', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make an OPTIONS preflight request
      const response = await request(transport['app'])
        .options('/health')
        .set('Origin', 'https://example.com')
        .set('Access-Control-Request-Method', 'GET')
        .set('Access-Control-Request-Headers', 'Content-Type, Authorization')
        .expect(200);

      // Assert: Should include proper CORS preflight headers
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toBe('GET, POST, DELETE, OPTIONS');
      expect(response.headers['access-control-allow-headers']).toContain('Content-Type');
      expect(response.headers['access-control-allow-headers']).toContain('Authorization');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-protocol-version');
    });

    it('should expose required headers for MCP clients', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a request that should expose MCP headers
      const response = await request(transport['app']).get('/health').expect(200);

      // Assert: Should expose MCP-specific headers
      expect(response.headers['access-control-expose-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-expose-headers']).toContain('mcp-protocol-version');
    });

    it('should include CORS headers on all endpoints', async () => {
      // Test multiple endpoints to ensure CORS is applied globally
      const endpoints = ['/health', '/auth/status', '/mcp/capabilities'];

      for (const endpoint of endpoints) {
        // Act: Make request to each endpoint
        const response = await request(transport['app']).get(endpoint);

        // Assert: Should include CORS headers regardless of status code
        expect(response.headers['access-control-allow-origin']).toBe('*');
        expect(response.headers['access-control-allow-methods']).toBe('GET, POST, DELETE, OPTIONS');
      }
    });

    it('should handle CORS for OAuth2 discovery endpoints', async () => {
      // Arrange: Server with OAuth2 enabled
      const oauth2Transport = new HttpMCPTransport(8081);
      await oauth2Transport.start();

      try {
        // Act: Make request to OAuth2 discovery endpoint
        const response = await request(oauth2Transport['app'])
          .get('/.well-known/oauth-protected-resource')
          .expect(200);

        // Assert: Should include CORS headers for OAuth2 discovery
        expect(response.headers['access-control-allow-origin']).toBe('*');
        expect(response.headers['access-control-allow-methods']).toContain('GET');
        expect(response.headers['access-control-expose-headers']).toContain('mcp-session-id');
        expect(response.headers['access-control-expose-headers']).toContain('mcp-protocol-version');
      } finally {
        await oauth2Transport.stop();
      }
    });

    it('should handle complex CORS scenarios with custom headers', async () => {
      // Arrange: Server is started and ready to handle requests

      // Act: Make a request with custom headers that should be allowed
      const response = await request(transport['app'])
        .get('/health')
        .set('Origin', 'https://mcp-client.example.com')
        .set('X-Requested-With', 'XMLHttpRequest')
        .set('mcp-session-id', 'test-session-123')
        .set('mcp-protocol-version', '1.0.0')
        .expect(200);

      // Assert: Should handle custom headers properly
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-headers']).toContain('X-Requested-With');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-session-id');
      expect(response.headers['access-control-allow-headers']).toContain('mcp-protocol-version');
    });
  });

  describe('OAuth2 Discovery', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should serve OAuth2 protected resource metadata when OAuth2 is enabled', async () => {
      // Arrange: Server is started with OAuth2 enabled

      // Act: Request OAuth2 protected resource metadata
      const response = await request(transport['app'])
        .get('/.well-known/oauth-protected-resource')
        .expect(200);

      // Assert: Should return OAuth2 resource metadata
      expect(response.body).toHaveProperty('resource');
      expect(response.body).toHaveProperty('authorization_servers');
      expect(response.body).toHaveProperty('scopes_supported');
    });
  });

  describe('Request Error Handling', () => {
    beforeEach(async () => {
      await transport.start();
    });

    it('should return bad request error when JSON payload is malformed', async () => {
      // Arrange: Prepare malformed JSON payload
      const malformedJson = 'invalid json';

      // Act: Send malformed JSON to MCP endpoint
      const response = await request(transport['app'])
        .post('/mcp')
        .set('Content-Type', 'application/json')
        .send(malformedJson)
        .expect(400);

      // Assert: Should return bad request error
      expect(response.text).toBeDefined();
    });

    it('should return bad request error when session ID is missing', async () => {
      // Arrange: No session ID provided in request

      // Act: Request MCP endpoint without session ID
      const response = await request(transport['app']).get('/mcp').expect(400);

      // Assert: Should return missing session ID error
      expect(response.text).toContain('Invalid or missing session ID');
    });
  });
});
