import { createServer } from '../src/server.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

// Mock the service adapter
jest.mock('../src/adapters/service-adapter.js', () => {
  const mockPing = jest.fn().mockResolvedValue({ success: true, message: 'pong' });
  const mockEcho = jest.fn().mockResolvedValue({ success: true, echo: 'test data' });
  const mockQueryOrderLists = jest.fn().mockResolvedValue({ orders: [], total: 0 });
  const mockGetOrderDetail = jest.fn().mockResolvedValue({ order: { vid: 'test-vid' } });
  const mockQueryStoreSubsidyAuditData = jest.fn().mockResolvedValue({ data: [], code: '0' });

  return {
    SERVICE_MODULES: {
      test: {
        clientId: 'test-client',
        functions: {
          ping: mockPing,
          echo: mockEcho,
        },
      },
      oms: {
        clientId: 'orders-portal',
        functions: {
          queryOrderLists: mockQueryOrderLists,
          getOrderDetail: mockGetOrderDetail,
          queryStoreSubsidyAuditData: mockQueryStoreSubsidyAuditData,
        },
      },
    },
    SERVICE_TOOL_CONFIGS: {
      test: [
        {
          name: 'ping',
          description: 'Quick ping test',
          zodSchema: undefined,
        },
        {
          name: 'echo',
          description: 'Echo test',
          zodSchema: undefined,
        },
      ],
      oms: [
        {
          name: 'queryOrderLists',
          description: 'Query order lists',
          zodSchema: undefined,
        },
        {
          name: 'getOrderDetail',
          description: 'Get order detail',
          zodSchema: undefined,
        },
        {
          name: 'queryStoreSubsidyAuditData',
          description: 'Query store subsidy audit data',
          zodSchema: undefined,
        },
      ],
    },
  };
});

describe('MCP Server', () => {
  let server: Server;
  let mockPing: jest.Mock;
  let mockEcho: jest.Mock;
  let mockQueryOrderLists: jest.Mock;
  let mockGetOrderDetail: jest.Mock;

  beforeEach(() => {
    server = createServer();

    // Get the mock functions from the mocked module
    const { SERVICE_MODULES } = require('../src/adapters/service-adapter.js');
    mockPing = SERVICE_MODULES.test.functions.ping;
    mockEcho = SERVICE_MODULES.test.functions.echo;
    mockQueryOrderLists = SERVICE_MODULES.oms.functions.queryOrderLists;
    mockGetOrderDetail = SERVICE_MODULES.oms.functions.getOrderDetail;
  });

  afterEach(() => {
    mockPing.mockClear();
    mockEcho.mockClear();
    mockQueryOrderLists.mockClear();
    mockGetOrderDetail.mockClear();
  });

  describe('Server Creation', () => {
    it('should create server with correct name and version', () => {
      expect(server).toBeInstanceOf(Server);
      // Server properties are private, but we can test behavior
    });

    it('should have tools capability enabled', () => {
      // Test that server has tools capability by checking if handlers are set
      expect(server).toBeDefined();
    });
  });

  describe('List Tools Handler', () => {
    it('should list all available tools', async () => {
      const mockRequest = {
        method: 'tools/list',
        params: {},
      };

      // Get the handler function
      const handler = (server as any)._requestHandlers.get('tools/list');
      expect(handler).toBeDefined();

      const result = await handler(mockRequest);

      expect(result).toHaveProperty('tools');
      expect(Array.isArray(result.tools)).toBe(true);
      expect(result.tools.length).toBeGreaterThan(0);

      // Check that tools have correct structure
      result.tools.forEach((tool: any) => {
        expect(tool).toHaveProperty('name');
        expect(tool).toHaveProperty('description');
        expect(typeof tool.name).toBe('string');
        expect(typeof tool.description).toBe('string');
      });
    });

    it('should include test tools', async () => {
      const mockRequest = {
        method: 'tools/list',
        params: {},
      };

      const handler = (server as any)._requestHandlers.get('tools/list');
      const result = await handler(mockRequest);

      const toolNames = result.tools.map((tool: any) => tool.name);
      expect(toolNames).toContain('test_ping');
      expect(toolNames).toContain('test_echo');
    });

    it('should include OMS tools', async () => {
      const mockRequest = {
        method: 'tools/list',
        params: {},
      };

      const handler = (server as any)._requestHandlers.get('tools/list');
      const result = await handler(mockRequest);

      const toolNames = result.tools.map((tool: any) => tool.name);
      expect(toolNames).toContain('oms_queryOrderLists');
      expect(toolNames).toContain('oms_getOrderDetail');
    });
  });

  describe('Call Tool Handler', () => {
    it('should execute test_ping tool successfully', async () => {
      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'test_ping',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('content');
      expect(Array.isArray(result.content)).toBe(true);
      expect(result.content[0]).toHaveProperty('type', 'text');
      expect(result.content[0]).toHaveProperty('text');

      // Parse the JSON response
      const responseData = JSON.parse(result.content[0].text);
      expect(responseData).toHaveProperty('success', true);
      expect(responseData).toHaveProperty('message', 'pong');
    });

    it('should execute test_echo tool with arguments', async () => {
      const testData = { message: 'hello world' };
      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'test_echo',
          arguments: testData,
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('content');
      const responseData = JSON.parse(result.content[0].text);
      expect(responseData).toHaveProperty('success', true);
      expect(responseData).toHaveProperty('echo', 'test data');
    });

    it('should handle unknown tool gracefully', async () => {
      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'unknown_tool',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('isError', true);
      expect(result.content[0].text).toContain('Error:');
    });

    it('should handle malformed tool name', async () => {
      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'invalid-name-format',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('isError', true);
    });

    it('should handle service function errors', async () => {
      // Mock a service function to throw an error
      mockPing.mockRejectedValueOnce(new Error('Service unavailable'));

      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'test_ping',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('isError', true);
      expect(result.content[0].text).toContain('Service unavailable');
    });
  });

  describe('Safe JSON Stringify', () => {
    it('should handle circular references', async () => {
      // Create a circular reference
      const circularObj: any = { name: 'test' };
      circularObj.self = circularObj;

      // Mock service to return circular object
      mockEcho.mockResolvedValueOnce(circularObj);

      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'test_echo',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      expect(result).toHaveProperty('content');
      expect(result.content[0].text).toContain('[Circular Reference]');
    });

    it('should handle Error objects', async () => {
      const errorObj = new Error('Test error');

      mockEcho.mockResolvedValueOnce(errorObj);

      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'test_echo',
          arguments: {},
        },
      };

      const handler = (server as any)._requestHandlers.get('tools/call');
      const result = await handler(mockRequest);

      const responseData = JSON.parse(result.content[0].text);
      expect(responseData).toHaveProperty('name', 'Error');
      expect(responseData).toHaveProperty('message', 'Test error');
    });
  });
});
