/**
 * Comprehensive MCP Server Blackbox Integration Tests
 *
 * Tests the complete MCP server functionality including:
 * 1. OAuth2 mode with current .env configs
 * 2. MCP client connection with streamable HTTP
 * 3. MCP client connection failures without token
 * 4. Connection auth mode scenarios
 */

import { spawn, ChildProcess } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve } from 'path';
import * as http from 'http';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

interface MCPResponse {
  jsonrpc: string;
  id: number;
  result?: any;
  error?: any;
}

class MCPBlackboxTestSuite {
  private results: TestResult[] = [];
  private serverProcess: ChildProcess | null = null;
  private originalEnv: string | null = null;
  private testPort = 6302; // Unique port for integration tests
  private serverUrl: string;
  private validToken: string | null = null;

  constructor() {
    this.serverUrl = `http://localhost:${this.testPort}`;
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting MCP Server Blackbox Integration Tests');
    console.log('='.repeat(60));

    try {
      await this.backupEnvironment();

      // Test Suite 1: OAuth2 Enabled Mode
      await this.testOAuth2EnabledMode();

      // Test Suite 2: OAuth2 Disabled Mode
      await this.testOAuth2DisabledMode();

      // Test Suite 3: Connection Auth Modes
      await this.testConnectionAuthModes();

      this.printSummary();
    } catch (error) {
      console.error(`❌ Test suite failed: ${error}`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  private async backupEnvironment(): Promise<void> {
    const envPath = resolve(process.cwd(), '.env');
    if (existsSync(envPath)) {
      this.originalEnv = readFileSync(envPath, 'utf8');
      console.log('💾 Backed up original .env file');
    }
  }

  private async restoreEnvironment(): Promise<void> {
    if (this.originalEnv) {
      const envPath = resolve(process.cwd(), '.env');
      writeFileSync(envPath, this.originalEnv);
      console.log('🔄 Restored original .env file');
    }
  }

  private async updateEnvironmentConfig(config: Record<string, string>): Promise<void> {
    const envPath = resolve(process.cwd(), '.env');
    const baseConfig = `# Test Configuration - Auto Generated
NODE_ENV=test
TRANSPORT=http
MCP_SERVER_PORT=${this.testPort}

# API Endpoints (from original .env)
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

# Authentication
AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# Permission Service
PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi

# Debug
DEBUG_SERVICE_ADAPTER=false
`;

    const configLines = Object.entries(config)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');

    writeFileSync(envPath, baseConfig + '\n' + configLines);
    console.log(`📝 Updated .env for test configuration`);
  }

  private async startServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log(`🚀 Starting MCP server on port ${this.testPort}...`);

      this.serverProcess = spawn('npm', ['run', 'dev'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_ENV: 'test' },
      });

      let serverReady = false;
      const timeout = setTimeout(() => {
        if (!serverReady) {
          reject(new Error('Server startup timeout'));
        }
      }, 30000);

      this.serverProcess.stdout?.on('data', data => {
        const output = data.toString();
        console.log(`[SERVER] ${output.trim()}`);

        if (
          output.includes(`HTTP MCP Transport on port ${this.testPort}`) ||
          output.includes('✅ orders-portal-mcp-server running')
        ) {
          serverReady = true;
          clearTimeout(timeout);
          // Wait a bit more for full initialization
          setTimeout(resolve, 2000);
        }
      });

      this.serverProcess.stderr?.on('data', data => {
        const output = data.toString();
        console.log(`[SERVER] ${output.trim()}`);

        if (
          output.includes(`HTTP MCP Transport on port ${this.testPort}`) ||
          output.includes('✅ orders-portal-mcp-server running')
        ) {
          serverReady = true;
          clearTimeout(timeout);
          setTimeout(resolve, 2000);
        }
      });

      this.serverProcess.on('error', reject);
      this.serverProcess.on('exit', code => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`Server exited with code ${code}`));
        }
      });
    });
  }

  private async stopServer(): Promise<void> {
    if (this.serverProcess) {
      console.log('🛑 Stopping MCP server...');
      this.serverProcess.kill('SIGTERM');

      return new Promise(resolve => {
        this.serverProcess!.on('exit', () => {
          this.serverProcess = null;
          resolve();
        });

        // Force kill after 5 seconds
        setTimeout(() => {
          if (this.serverProcess) {
            this.serverProcess.kill('SIGKILL');
            this.serverProcess = null;
          }
          resolve();
        }, 5000);
      });
    }
  }

  private async makeHttpRequest(
    path: string,
    options: {
      method?: string;
      headers?: Record<string, string>;
      body?: any;
    } = {}
  ): Promise<{ status: number; data: any; headers: any }> {
    const { method = 'GET', headers = {}, body } = options;

    return new Promise((resolve, reject) => {
      const requestData = body ? JSON.stringify(body) : undefined;

      const requestOptions: http.RequestOptions = {
        hostname: 'localhost',
        port: this.testPort,
        path,
        method,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json, text/event-stream',
          ...headers,
          ...(requestData && { 'Content-Length': Buffer.byteLength(requestData) }),
        },
      };

      const req = http.request(requestOptions, res => {
        let responseData = '';

        res.on('data', chunk => {
          responseData += chunk;
        });

        res.on('end', () => {
          let parsedData;
          try {
            parsedData = JSON.parse(responseData);
          } catch {
            parsedData = responseData;
          }

          resolve({
            status: res.statusCode || 0,
            data: parsedData,
            headers: res.headers,
          });
        });
      });

      req.on('error', reject);

      if (requestData) {
        req.write(requestData);
      }

      req.end();
    });
  }

  private async makeMCPRequest(
    method: string,
    params: any = {},
    headers: Record<string, string> = {}
  ): Promise<{ status: number; response: MCPResponse | string }> {
    const requestBody = {
      jsonrpc: '2.0',
      id: Math.floor(Math.random() * 1000),
      method,
      params,
    };

    const result = await this.makeHttpRequest('/mcp', {
      method: 'POST',
      headers,
      body: requestBody,
    });

    // Handle Server-Sent Events response
    if (typeof result.data === 'string' && result.data.includes('data: ')) {
      const lines = result.data.split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const jsonData = JSON.parse(line.substring(6));
            return { status: result.status, response: jsonData };
          } catch (e) {
            // Continue parsing
          }
        }
      }
    }

    return { status: result.status, response: result.data };
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    console.log(`  🧪 ${name}...`);

    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({ name, status: 'PASS', duration });
      console.log(`  ✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error instanceof Error ? error.message : String(error),
      });
      console.log(`  ❌ ${name} (${duration}ms): ${error}`);
    }
  }

  // Test Suite 1: OAuth2 Enabled Mode
  private async testOAuth2EnabledMode(): Promise<void> {
    console.log('\n📋 Test Suite 1: OAuth2 Enabled Mode');
    console.log('-'.repeat(40));

    await this.updateEnvironmentConfig({
      OAUTH2_ENABLED: 'true',
      KEYCLOAK_BASE_URL: 'https://keycloak.ingka-dt.cn/auth',
      KEYCLOAK_REALM: 'master',
      OAUTH2_CLIENT_ID: 'mcp-mpc-odi',
      OAUTH2_CLIENT_AUTH_METHOD: 'none',
      OAUTH2_ADMIN_SCOPES: 'admin,roles',
      OAUTH2_WRITE_SCOPES: 'write',
      CONNECTION_AUTH_ENABLED: 'false',
      PER_REQUEST_AUTH_ENABLED: 'true',
    });

    await this.startServer();

    try {
      await this.runTest('Server health check', async () => {
        const result = await this.makeHttpRequest('/health');
        if (result.status !== 200) throw new Error(`Health check failed: ${result.status}`);
        if (!result.data.status || result.data.status !== 'healthy') {
          throw new Error('Server not healthy');
        }
      });

      await this.runTest('OAuth2 discovery endpoint', async () => {
        const result = await this.makeHttpRequest('/.well-known/oauth-protected-resource');
        if (result.status !== 200) throw new Error(`Discovery failed: ${result.status}`);
        if (!result.data.resource) throw new Error('Missing resource in discovery response');
      });

      await this.runTest('MCP initialize without token should fail', async () => {
        const { status, response } = await this.makeMCPRequest('initialize', {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' },
        });

        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
        if (typeof response === 'object' && response.error !== 'invalid_token') {
          throw new Error('Expected invalid_token error');
        }
      });

      await this.runTest('MCP initialize with invalid token should fail', async () => {
        const { status, response } = await this.makeMCPRequest(
          'initialize',
          {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test-client', version: '1.0.0' },
          },
          {
            Authorization: 'Bearer invalid-token',
          }
        );

        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
      });

      await this.runTest('Tools list without token should fail', async () => {
        const { status } = await this.makeMCPRequest('tools/list');
        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
      });

      await this.runTest('Tool call without token should fail', async () => {
        const { status } = await this.makeMCPRequest('tools/call', {
          name: 'test_ping',
          arguments: {},
        });
        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
      });
    } finally {
      await this.stopServer();
    }
  }

  // Test Suite 2: OAuth2 Disabled Mode
  private async testOAuth2DisabledMode(): Promise<void> {
    console.log('\n📋 Test Suite 2: OAuth2 Disabled Mode');
    console.log('-'.repeat(40));

    await this.updateEnvironmentConfig({
      OAUTH2_ENABLED: 'false',
      CONNECTION_AUTH_ENABLED: 'false',
      PER_REQUEST_AUTH_ENABLED: 'false',
    });

    await this.startServer();

    try {
      await this.runTest('Server health check', async () => {
        const result = await this.makeHttpRequest('/health');
        if (result.status !== 200) throw new Error(`Health check failed: ${result.status}`);
      });

      await this.runTest('MCP initialize without auth should work', async () => {
        const { status, response } = await this.makeMCPRequest('initialize', {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' },
        });

        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        if (typeof response === 'object' && !response.result) {
          throw new Error('Missing result in initialize response');
        }
      });

      await this.runTest('Tools list should work', async () => {
        const { status, response } = await this.makeMCPRequest('tools/list');
        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        if (typeof response === 'object' && !response.result?.tools) {
          throw new Error('Missing tools in response');
        }
      });

      await this.runTest('Test ping tool should work', async () => {
        const { status, response } = await this.makeMCPRequest('tools/call', {
          name: 'test_ping',
          arguments: {},
        });
        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        if (typeof response === 'object' && !response.result) {
          throw new Error('Missing result in tool call response');
        }
      });

      await this.runTest('Test echo tool should work', async () => {
        const { status, response } = await this.makeMCPRequest('tools/call', {
          name: 'test_echo',
          arguments: { message: 'hello world' },
        });
        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        if (typeof response === 'object' && !response.result) {
          throw new Error('Missing result in echo tool response');
        }
      });
    } finally {
      await this.stopServer();
    }
  }

  // Test Suite 3: Connection Auth Modes
  private async testConnectionAuthModes(): Promise<void> {
    console.log('\n📋 Test Suite 3: Connection Auth Modes');
    console.log('-'.repeat(40));

    // Test 3.1: Connection Auth Enabled (OAuth2 disabled)
    console.log('\n  🔒 Testing Connection Auth Enabled');
    await this.updateEnvironmentConfig({
      OAUTH2_ENABLED: 'false',
      CONNECTION_AUTH_ENABLED: 'true',
      CONNECTION_AUTH_STRICT: 'false',
      PER_REQUEST_AUTH_ENABLED: 'false',
    });

    await this.startServer();

    try {
      await this.runTest('MCP initialize without cookies should fail', async () => {
        const { status } = await this.makeMCPRequest('initialize', {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' },
        });

        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
      });

      await this.runTest('MCP initialize with valid cookies should work', async () => {
        const { status, response } = await this.makeMCPRequest(
          'initialize',
          {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test-client', version: '1.0.0' },
          },
          {
            Cookie:
              'test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=',
          }
        );

        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        if (typeof response === 'object' && !response.result) {
          throw new Error('Missing result in initialize response');
        }
      });
    } finally {
      await this.stopServer();
    }

    // Test 3.2: Connection Auth Strict Mode
    console.log('\n  🔒 Testing Connection Auth Strict Mode');
    await this.updateEnvironmentConfig({
      OAUTH2_ENABLED: 'false',
      CONNECTION_AUTH_ENABLED: 'true',
      CONNECTION_AUTH_STRICT: 'true',
      PER_REQUEST_AUTH_ENABLED: 'false',
    });

    await this.startServer();

    try {
      await this.runTest(
        'MCP initialize with invalid cookies should fail in strict mode',
        async () => {
          const { status } = await this.makeMCPRequest(
            'initialize',
            {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: { name: 'test-client', version: '1.0.0' },
            },
            {
              Cookie: 'test_orders-portal=invalid-cookie-value',
            }
          );

          if (status !== 401) throw new Error(`Expected 401, got ${status}`);
        }
      );
    } finally {
      await this.stopServer();
    }

    // Test 3.3: Per-Request Auth Mode
    console.log('\n  🔒 Testing Per-Request Auth Mode');
    await this.updateEnvironmentConfig({
      OAUTH2_ENABLED: 'false',
      CONNECTION_AUTH_ENABLED: 'false',
      PER_REQUEST_AUTH_ENABLED: 'true',
      PER_REQUEST_AUTH_CACHE_ENABLED: 'true',
    });

    await this.startServer();

    try {
      await this.runTest(
        'MCP initialize should work without auth (connection not protected)',
        async () => {
          const { status, response } = await this.makeMCPRequest('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test-client', version: '1.0.0' },
          });

          if (status !== 200) throw new Error(`Expected 200, got ${status}`);
        }
      );

      await this.runTest('Tool calls should require auth in per-request mode', async () => {
        const { status } = await this.makeMCPRequest('tools/call', {
          name: 'test_ping',
          arguments: {},
        });

        if (status !== 401) throw new Error(`Expected 401, got ${status}`);
      });

      await this.runTest('Tool calls with cookies should work in per-request mode', async () => {
        const { status, response } = await this.makeMCPRequest(
          'tools/call',
          {
            name: 'test_ping',
            arguments: {},
          },
          {
            Cookie:
              'test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=',
          }
        );

        if (status !== 200) throw new Error(`Expected 200, got ${status}`);
      });
    } finally {
      await this.stopServer();
    }
  }

  private printSummary(): void {
    console.log('\n📊 Test Results Summary');
    console.log('='.repeat(60));

    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;

    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️  Skipped: ${skipped}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  • ${r.name}: ${r.error}`);
        });
    }

    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / total;
    console.log(`\n⏱️  Average Duration: ${avgDuration.toFixed(0)}ms`);
  }

  private async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up...');

    if (this.serverProcess) {
      await this.stopServer();
    }

    await this.restoreEnvironment();
    console.log('✅ Cleanup complete');
  }
}

// Jest test wrapper
describe('MCP Server Blackbox Integration Tests', () => {
  let testSuite: MCPBlackboxTestSuite;

  beforeAll(() => {
    testSuite = new MCPBlackboxTestSuite();
  });

  it('should run all blackbox tests successfully', async () => {
    await testSuite.runAllTests();

    // Check if any tests failed
    const results = (testSuite as any).results as TestResult[];
    const failedTests = results.filter(r => r.status === 'FAIL');

    if (failedTests.length > 0) {
      const failureMessages = failedTests.map(t => `${t.name}: ${t.error}`).join('\n');
      throw new Error(`${failedTests.length} tests failed:\n${failureMessages}`);
    }
  }, 300000); // 5 minute timeout for all tests
});
