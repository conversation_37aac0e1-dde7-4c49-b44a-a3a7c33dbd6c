/**
 * MCP OAuth2 Client Integration Tests
 *
 * Tests OAuth2 authentication flows and streamable HTTP client connections
 * Simulates real MCP client behavior with proper OAuth2 token handling
 */

import { spawn, ChildProcess } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve } from 'path';

interface MCPSession {
  sessionId: string;
  transport: any;
}

interface OAuth2Token {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

class MCPOAuth2ClientTest {
  private serverProcess: ChildProcess | null = null;
  private originalEnv: string | null = null;
  private testPort = 6303;
  private serverUrl: string;
  private sessions: Map<string, MCPSession> = new Map();

  constructor() {
    this.serverUrl = `http://localhost:${this.testPort}`;
  }

  async runOAuth2Tests(): Promise<void> {
    console.log('🔐 Starting MCP OAuth2 Client Integration Tests');
    console.log('='.repeat(60));

    try {
      await this.backupEnvironment();
      await this.setupOAuth2Environment();
      await this.startServer();

      // Test OAuth2 discovery and metadata
      await this.testOAuth2Discovery();

      // Test MCP client connection flows
      await this.testMCPClientConnections();

      // Test streamable HTTP behavior
      await this.testStreamableHTTPBehavior();

      // Test session management
      await this.testSessionManagement();

      console.log('✅ All OAuth2 client tests completed');
    } catch (error) {
      console.error(`❌ OAuth2 client tests failed: ${error}`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  private async backupEnvironment(): Promise<void> {
    const envPath = resolve(process.cwd(), '.env');
    if (existsSync(envPath)) {
      this.originalEnv = readFileSync(envPath, 'utf8');
    }
  }

  private async setupOAuth2Environment(): Promise<void> {
    const envPath = resolve(process.cwd(), '.env');
    const config = `# OAuth2 Test Configuration
NODE_ENV=test
TRANSPORT=http
MCP_SERVER_PORT=${this.testPort}

# API Endpoints
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

# OAuth2 Configuration (from current .env)
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-mpc-odi
OAUTH2_CLIENT_AUTH_METHOD=none
OAUTH2_ADMIN_SCOPES=admin,roles
OAUTH2_WRITE_SCOPES=write
OAUTH2_DEFAULT_SCOPE_VALIDATION=any

# Connection and Request Auth
CONNECTION_AUTH_ENABLED=false
CONNECTION_AUTH_STRICT=false
PER_REQUEST_AUTH_ENABLED=true
PER_REQUEST_AUTH_CACHE_ENABLED=true

# Debug
DEBUG_SERVICE_ADAPTER=false
DEBUG_OAUTH2=true
`;

    writeFileSync(envPath, config);
    console.log('📝 Set up OAuth2 test environment');
  }

  private async startServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log(`🚀 Starting OAuth2-enabled MCP server...`);

      this.serverProcess = spawn('npm', ['run', 'dev'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_ENV: 'test' },
      });

      let serverReady = false;
      const timeout = setTimeout(() => {
        if (!serverReady) {
          reject(new Error('OAuth2 server startup timeout'));
        }
      }, 45000); // Longer timeout for OAuth2 setup

      this.serverProcess.stdout?.on('data', data => {
        const output = data.toString();
        console.log(`[SERVER] ${output.trim()}`);

        if (output.includes('OAuth2 enabled') || output.includes('HTTP MCP Transport on port')) {
          serverReady = true;
          clearTimeout(timeout);
          setTimeout(resolve, 3000); // Wait for OAuth2 setup
        }
      });

      this.serverProcess.stderr?.on('data', data => {
        const output = data.toString();
        console.log(`[SERVER] ${output.trim()}`);

        if (output.includes('OAuth2 enabled') || output.includes('HTTP MCP Transport on port')) {
          serverReady = true;
          clearTimeout(timeout);
          setTimeout(resolve, 3000);
        }
      });

      this.serverProcess.on('error', reject);
    });
  }

  private async testOAuth2Discovery(): Promise<void> {
    console.log('\n🔍 Testing OAuth2 Discovery');
    console.log('-'.repeat(30));

    // Use dynamic import for node-fetch
    const { default: fetch } = await import('node-fetch');

    // Test OAuth2 Protected Resource Metadata
    console.log('  📋 Testing OAuth2 protected resource metadata...');
    const metadataResponse = await fetch(`${this.serverUrl}/.well-known/oauth-protected-resource`);

    if (metadataResponse.status !== 200) {
      throw new Error(`OAuth2 metadata endpoint failed: ${metadataResponse.status}`);
    }

    const metadata = (await metadataResponse.json()) as any;
    console.log('  ✅ OAuth2 metadata retrieved:', {
      resource: metadata.resource,
      authorization_servers: metadata.authorization_servers,
      scopes_supported: metadata.scopes_supported,
    });

    // Test MCP capabilities endpoint
    console.log('  📋 Testing MCP capabilities...');
    const capabilitiesResponse = await fetch(`${this.serverUrl}/mcp/capabilities`);

    if (capabilitiesResponse.status !== 200) {
      throw new Error(`MCP capabilities endpoint failed: ${capabilitiesResponse.status}`);
    }

    const capabilities = await capabilitiesResponse.json();
    console.log('  ✅ MCP capabilities retrieved:', capabilities);
  }

  private async testMCPClientConnections(): Promise<void> {
    console.log('\n🤖 Testing MCP Client Connections');
    console.log('-'.repeat(35));

    // Test 1: Connection without token should fail
    console.log('  🚫 Testing connection without token...');
    const noTokenResponse = await this.makeMCPRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' },
    });

    if (noTokenResponse.status !== 401) {
      throw new Error(`Expected 401 for no token, got ${noTokenResponse.status}`);
    }
    console.log('  ✅ Connection properly rejected without token');

    // Test 2: Connection with invalid token should fail
    console.log('  🚫 Testing connection with invalid token...');
    const invalidTokenResponse = await this.makeMCPRequest(
      'initialize',
      {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: { name: 'test-client', version: '1.0.0' },
      },
      {
        Authorization: 'Bearer invalid-token-12345',
      }
    );

    if (invalidTokenResponse.status !== 401) {
      throw new Error(`Expected 401 for invalid token, got ${invalidTokenResponse.status}`);
    }
    console.log('  ✅ Connection properly rejected with invalid token');

    // Test 3: Test WWW-Authenticate header
    console.log('  🔍 Testing WWW-Authenticate header...');
    const authHeader = invalidTokenResponse.headers['www-authenticate'];
    if (!authHeader || !authHeader.includes('Bearer')) {
      throw new Error('Missing or invalid WWW-Authenticate header');
    }
    console.log('  ✅ WWW-Authenticate header present:', authHeader);
  }

  private async testStreamableHTTPBehavior(): Promise<void> {
    console.log('\n🌊 Testing Streamable HTTP Behavior');
    console.log('-'.repeat(35));

    // Test Server-Sent Events response format
    console.log('  📡 Testing SSE response format...');
    const { default: fetch } = await import('node-fetch');
    const response = await fetch(`${this.serverUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'sse-test-client', version: '1.0.0' },
        },
      }),
    });

    const responseText = await response.text();
    console.log('  📄 Response format:', {
      status: response.status,
      contentType: response.headers.get('content-type'),
      hasSSEFormat: responseText.includes('data: '),
    });

    if (response.status === 401) {
      console.log('  ✅ SSE format maintained for auth errors');
    }

    // Test session handling
    console.log('  🔗 Testing session management...');
    const sessionHeader = response.headers.get('mcp-session-id');
    if (sessionHeader) {
      console.log('  ✅ Session ID provided:', sessionHeader);
    } else {
      console.log('  ℹ️  No session ID (expected for failed auth)');
    }
  }

  private async testSessionManagement(): Promise<void> {
    console.log('\n🔄 Testing Session Management');
    console.log('-'.repeat(30));

    // Test session cleanup on auth failure
    console.log('  🧹 Testing session cleanup...');
    const { default: fetch } = await import('node-fetch');
    const healthResponse = await fetch(`${this.serverUrl}/health`);
    const health = (await healthResponse.json()) as any;

    console.log('  📊 Server health:', {
      status: health.status,
      activeSessions: health.activeSessions,
      sessions: health.sessions,
    });

    if (health.activeSessions > 0) {
      console.log('  ⚠️  Warning: Active sessions found (may be from previous tests)');
    } else {
      console.log('  ✅ No active sessions (proper cleanup)');
    }
  }

  private async makeMCPRequest(
    method: string,
    params: any = {},
    headers: Record<string, string> = {}
  ): Promise<{
    status: number;
    data: any;
    headers: Record<string, string>;
  }> {
    const requestBody = {
      jsonrpc: '2.0',
      id: Math.floor(Math.random() * 1000),
      method,
      params,
    };

    const { default: fetch } = await import('node-fetch');
    const response = await fetch(`${this.serverUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
        ...headers,
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    return {
      status: response.status,
      data: responseData,
      headers: Object.fromEntries(response.headers.entries()),
    };
  }

  private async stopServer(): Promise<void> {
    if (this.serverProcess) {
      console.log('🛑 Stopping OAuth2 server...');
      this.serverProcess.kill('SIGTERM');

      return new Promise(resolve => {
        this.serverProcess!.on('exit', () => {
          this.serverProcess = null;
          resolve();
        });

        setTimeout(() => {
          if (this.serverProcess) {
            this.serverProcess.kill('SIGKILL');
            this.serverProcess = null;
          }
          resolve();
        }, 5000);
      });
    }
  }

  private async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up OAuth2 tests...');

    if (this.serverProcess) {
      await this.stopServer();
    }

    if (this.originalEnv) {
      const envPath = resolve(process.cwd(), '.env');
      writeFileSync(envPath, this.originalEnv);
      console.log('🔄 Restored original .env file');
    }

    console.log('✅ OAuth2 test cleanup complete');
  }
}

// Jest test wrapper
describe('MCP OAuth2 Client Integration Tests', () => {
  let testSuite: MCPOAuth2ClientTest;

  beforeAll(() => {
    testSuite = new MCPOAuth2ClientTest();
  });

  it('should handle OAuth2 authentication flows correctly', async () => {
    await testSuite.runOAuth2Tests();
  }, 180000); // 3 minute timeout
});
