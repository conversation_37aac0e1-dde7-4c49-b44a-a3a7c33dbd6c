import { SERVICE_MODULES, SERVICE_TOOL_CONFIGS } from '../../src/adapters/service-adapter.js';

// Mock dependencies
jest.mock('../../src/auth/cookie-formatter.js', () => ({
  getAuthCookiesWithLogging: jest.fn().mockReturnValue('mock-cookies'),
  createAuthCookiesContext: jest.fn().mockReturnValue({}),
}));

jest.mock('../../src/auth/auth-context.js', () => ({
  getCurrentAuthInfo: jest.fn().mockReturnValue(null),
  getAuthCookiesFromContext: jest.fn().mockReturnValue(null),
}));

jest.mock('../../src/auth/permission-service.js', () => ({
  globalMcpUserTokenService: {
    shouldRefreshToken: jest.fn().mockReturnValue(false),
    refreshMcpUserToken: jest.fn().mockResolvedValue(undefined),
  },
}));

const mockHttpFunction = jest.fn().mockResolvedValue({ success: true, data: 'mock response' });

// Mock the HTTP module properly for dynamic imports
jest.mock('../../src/utils/http.js', () => mockHttpFunction);

describe('Service Adapter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Module Configuration', () => {
    it('should provide all required service modules when adapter is loaded', () => {
      // Arrange: Service adapter is imported

      // Act: Access service modules
      const modules = SERVICE_MODULES;

      // Assert: Should contain all required service modules
      expect(modules).toHaveProperty('oms');
      expect(modules).toHaveProperty('global');
      expect(modules).toHaveProperty('test');
      expect(typeof modules.oms.functions.queryOrderLists).toBe('function');
      expect(typeof modules.oms.functions.getCurrentUser).toBe('function');
      expect(typeof modules.test.functions.ping).toBe('function');
    });
  });

  describe('Tool Configuration Validation', () => {
    it('should provide valid tool configurations with schemas when adapter is loaded', () => {
      // Arrange: Service adapter is imported

      // Act: Access tool configurations
      const toolConfigs = SERVICE_TOOL_CONFIGS;

      // Assert: Should contain valid tool configurations with schemas
      expect(toolConfigs).toHaveProperty('oms');
      expect(toolConfigs).toHaveProperty('global');
      expect(toolConfigs).toHaveProperty('test');

      const omsConfigs = toolConfigs.oms;
      expect(Array.isArray(omsConfigs)).toBe(true);
      expect(omsConfigs.length).toBeGreaterThan(0);

      const queryOrderListsConfig = omsConfigs.find(c => c.name === 'queryOrderLists');
      expect(queryOrderListsConfig).toHaveProperty('zodSchema');
      expect(queryOrderListsConfig).toHaveProperty('name');
      expect(queryOrderListsConfig).toHaveProperty('description');

      const getCurrentUserConfig = omsConfigs.find(c => c.name === 'getCurrentUser');
      expect(getCurrentUserConfig).toHaveProperty('zodSchema');
      expect(getCurrentUserConfig).toHaveProperty('name');
      expect(getCurrentUserConfig).toHaveProperty('description');

      // Global module should be empty now
      const globalConfigs = toolConfigs.global;
      expect(Array.isArray(globalConfigs)).toBe(true);
      expect(globalConfigs.length).toBe(0);
    });
  });

  describe('Test Service Functions', () => {
    it('should return pong response when ping is called with test data', async () => {
      // Arrange: Set test data for ping function
      const testData = { test: 'value' };

      // Act: Call ping function with test data
      const result = await SERVICE_MODULES.test.functions.ping(testData);

      // Assert: Should return successful pong response with data
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'pong');
      expect(result).toHaveProperty('data', testData);
      expect(result).toHaveProperty('timestamp');
    });

    it('should return echo response when echo is called with message data', async () => {
      // Arrange: Set message data for echo function
      const messageData = { message: 'hello', value: 123 };

      // Act: Call echo function with message data
      const result = await SERVICE_MODULES.test.functions.echo(messageData);

      // Assert: Should return successful echo response with original data
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'echo response');
      expect(result).toHaveProperty('echo', messageData);
      expect(result).toHaveProperty('timestamp');
    });
  });

  describe('HTTP Service Functions', () => {
    beforeEach(() => {
      mockHttpFunction.mockClear();
    });

    it('should call order search API when queryOrderLists is called with custom parameters', async () => {
      // Arrange: Set custom search parameters
      const searchParams = {
        page: 2,
        pageSize: 20,
        orderStatus: 'completed',
        storeCode: 'STORE001',
      };

      // Act: Call queryOrderLists with custom parameters
      await SERVICE_MODULES.oms.functions.queryOrderLists(searchParams);

      // Assert: Should call HTTP function with correct API parameters
      expect(mockHttpFunction).toHaveBeenCalledWith({
        url: '/orders/search',
        method: 'POST',
        data: {
          page: 2,
          pageSize: 20,
          orderStatus: 'completed',
          storeCode: 'STORE001',
          startDate: undefined,
          endDate: undefined,
        },
        timeout: 30000,
        useKong: true,
        cookies: 'mock-cookies',
      });
    });

    it('should call order detail API when getOrderDetail is called with order VID', async () => {
      // Arrange: Set order VID for detail lookup
      const orderVid = 'ORDER-123';

      // Act: Call getOrderDetail with order VID
      await SERVICE_MODULES.oms.functions.getOrderDetail({ vid: orderVid });

      // Assert: Should call HTTP function with correct detail API parameters
      expect(mockHttpFunction).toHaveBeenCalledWith({
        url: '/order/detail',
        method: 'POST',
        data: {
          vid: orderVid,
          orderNO: undefined,
          ikeaOrderNO: undefined,
        },
        timeout: 30000,
        useKong: true,
        cookies: 'mock-cookies',
      });
    });

    it('should call store subsidy audit API when queryStoreSubsidyAuditData is called', async () => {
      // Arrange: Set store subsidy audit parameters
      const auditParams = {
        storeId: '621',
        fromTimeStamp: 1754668800000,
        endTimeStamp: 1754755200000,
      };

      // Act: Call queryStoreSubsidyAuditData with parameters
      await SERVICE_MODULES.oms.functions.queryStoreSubsidyAuditData(auditParams);

      // Assert: Should call HTTP function with correct audit API parameters
      expect(mockHttpFunction).toHaveBeenCalledWith({
        url: '/order-web/toolkit/storeSubsidyAuditData/query',
        method: 'POST',
        data: {
          storeId: '621',
          fromTimeStamp: 1754668800000,
          endTimeStamp: 1754755200000,
        },
        timeout: 30000,
        useKong: true,
        cookies: 'mock-cookies',
      });
    });
  });

  describe('Error Handling', () => {
    it('should propagate network error when HTTP request fails', async () => {
      // Arrange: Mock HTTP function to reject with network error
      mockHttpFunction.mockRejectedValueOnce(new Error('Network error'));

      // Act & Assert: Should propagate the network error
      await expect(SERVICE_MODULES.oms.functions.queryOrderLists({})).rejects.toThrow(
        'Network error'
      );
    });
  });
});
