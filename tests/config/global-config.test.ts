import {
  globalConfig,
  getGlobalConfig,
  getConfigValue,
  initializeGlobalConfig,
} from '../../src/config/global-config.js';

// Mock the environment utilities
jest.mock('../../src/utils/env.js', () => ({
  getEnvironmentConfig: jest.fn(),
}));

describe('Global Configuration Manager', () => {
  let mockGetEnvironmentConfig: jest.Mock;

  beforeEach(() => {
    // Arrange: Reset state and get mock reference
    globalConfig.reset();
    jest.clearAllMocks();
    mockGetEnvironmentConfig = require('../../src/utils/env.js').getEnvironmentConfig;
  });

  describe('Configuration Initialization', () => {
    it('should initialize configuration when environment config is valid', async () => {
      // Arrange: Set up valid environment configuration
      const validConfig = {
        NODE_ENV: 'test',
        TRANSPORT: 'http',
        MCP_SERVER_PORT: 8080,
        OAUTH2_ENABLED: true,
      };
      mockGetEnvironmentConfig.mockResolvedValue(validConfig);

      // Act: Initialize configuration
      await initializeGlobalConfig();

      // Assert: Configuration should be initialized
      expect(globalConfig.isInitialized()).toBe(true);
    });

    it('should prevent reinitialization when already initialized', async () => {
      // Arrange: Initialize configuration first
      mockGetEnvironmentConfig.mockResolvedValue({ NODE_ENV: 'test' });
      await initializeGlobalConfig();
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Act: Attempt to initialize again
      await initializeGlobalConfig();

      // Assert: Should warn about reinitialization
      expect(consoleSpy).toHaveBeenCalledWith('⚠️ GlobalConfigManager already initialized');
      consoleSpy.mockRestore();
    });

    it('should throw error when environment config fails to load', async () => {
      // Arrange: Mock environment config to fail
      mockGetEnvironmentConfig.mockRejectedValue(new Error('NACOS connection failed'));

      // Act & Assert: Should throw configuration initialization error
      await expect(initializeGlobalConfig()).rejects.toThrow('Configuration initialization failed');
      expect(globalConfig.isInitialized()).toBe(false);
    });
  });

  describe('Configuration Access', () => {
    beforeEach(() => {
      // Reset configuration and enable test mode before each test
      globalConfig.reset();
      globalConfig.enableTestMode();
    });

    afterEach(() => {
      // Disable test mode after each test
      globalConfig.disableTestMode();
    });

    // Table-driven test for configuration value retrieval
    const configTestCases = [
      { key: 'NODE_ENV', expectedValue: 'production', expectedType: 'string' },
      { key: 'MCP_SERVER_PORT', expectedValue: 3000, expectedType: 'number' },
      { key: 'OAUTH2_ENABLED', expectedValue: false, expectedType: 'boolean' },
      { key: 'TRANSPORT', expectedValue: 'http', expectedType: 'string' },
    ];

    it.each(configTestCases)(
      'should retrieve $key with value $expectedValue and type $expectedType',
      async ({ key, expectedValue, expectedType }) => {
        // Arrange: Set up configuration with specific values
        const testConfig = { [key]: expectedValue };
        mockGetEnvironmentConfig.mockResolvedValue(testConfig);
        await initializeGlobalConfig();

        // Act: Retrieve configuration value
        const actualValue = getConfigValue(key as any);

        // Assert: Value and type should match expectations
        expect(actualValue).toBe(expectedValue);
        expect(typeof actualValue).toBe(expectedType);
      }
    );

    it('should throw error when accessing uninitialized configuration', () => {
      // Arrange: Ensure configuration is not initialized
      globalConfig.reset();

      // Act & Assert: Should throw error for uninitialized access
      expect(() => getGlobalConfig()).toThrow('Configuration not initialized');
      expect(() => getConfigValue('NODE_ENV')).toThrow('Configuration not initialized');
    });
  });

  describe('Configuration Reset', () => {
    beforeEach(() => {
      // Reset configuration and enable test mode before each test
      globalConfig.reset();
      globalConfig.enableTestMode();
    });

    afterEach(() => {
      // Disable test mode after each test
      globalConfig.disableTestMode();
    });

    it('should reset configuration state and allow reinitialization', async () => {
      // Arrange: Initialize configuration first
      mockGetEnvironmentConfig.mockResolvedValue({ NODE_ENV: 'test' });
      await initializeGlobalConfig();
      expect(globalConfig.isInitialized()).toBe(true);

      // Act: Reset configuration
      globalConfig.reset();

      // Assert: Should be uninitialized and allow reinitialization
      expect(globalConfig.isInitialized()).toBe(false);
      expect(() => getGlobalConfig()).toThrow('Configuration not initialized');

      // Should allow reinitialization
      await initializeGlobalConfig();
      expect(globalConfig.isInitialized()).toBe(true);
    });
  });
});
