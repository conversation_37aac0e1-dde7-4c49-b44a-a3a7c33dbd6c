/**
 * Jest test setup file
 * This file runs before each test suite
 */

// Mock environment variables for consistent testing
process.env.NODE_ENV = 'test';
process.env.MCP_SERVER_PORT = '8080';
process.env.MCP_SERVER_BASE_URL = 'http://localhost:8080';
process.env.MCP_SERVER_PUBLIC_BASE_URL = 'http://localhost:8080';
process.env.OAUTH2_ENABLED = 'false';
process.env.CONNECTION_AUTH_ENABLED = 'false';
process.env.TRANSPORT = 'http';

// Suppress console.error during tests unless explicitly needed
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  console.error = jest.fn();
  console.log = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
});

// Global test utilities
(global as any).testUtils = {
  createMockRequest: (overrides = {}) => ({
    headers: {},
    body: {},
    query: {},
    params: {},
    ...overrides,
  }),

  createMockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
    };
    return res;
  },
};
