/**
 * Unit tests for environment utilities
 * Testing pure functions that validate and transform configuration
 */

import {
  validateEnvironmentWithNacos,
  logEnvironmentInfoWithNacos,
  convertConfigTypes,
  EnvironmentConfig,
} from '../../src/utils/env';

// Helper function for creating mock configs
const createMockConfig = (overrides: Partial<EnvironmentConfig> = {}): EnvironmentConfig => ({
  NODE_ENV: 'test',
  VITE_API_HOST: 'https://api.example.com',
  VITE_API_HOST_KONG: 'https://kong.example.com',
  VITE_MASTER_DATA_API_HOST: 'https://master.example.com',
  VITE_MASTER_DATA_API_KEY: 'test-key',
  AUTH_COOKIES: 'test-cookies',
  X_CUSTOM_REFERRER: 'test-referrer',
  DEBUG_SERVICE_ADAPTER: false,
  TRANSPORT: 'http',
  MCP_SERVER_PORT: 8080,
  MCP_SERVER_BASE_URL: 'http://localhost:8080',
  MCP_SERVER_PUBLIC_BASE_URL: 'http://localhost:8080',
  OAUTH2_ENABLED: false,
  KEYCLOAK_BASE_URL: 'https://keycloak.example.com',
  KEYCLOAK_REALM: 'test',
  // Note: OAUTH2_CLIENT_ID removed - now provided by clients in requests
  OAUTH2_CLIENT_SECRET: 'test-secret-with-sufficient-length',
  OAUTH2_CLIENT_AUTH_METHOD: 'client_secret_basic',
  // Note: OAUTH2_REDIRECT_URI and OAUTH2_SCOPES removed - now provided by clients in requests
  OAUTH2_ISSUER_URL: 'https://keycloak.example.com/realms/test',
  CONNECTION_AUTH_ENABLED: false,
  CONNECTION_AUTH_STRICT: false,
  CONNECTION_AUTH_TEST_API: false,
  PER_REQUEST_AUTH_ENABLED: false,
  PER_REQUEST_AUTH_CACHE_ENABLED: true,
  PER_REQUEST_AUTH_CACHE_MAX_AGE: 300,
  PER_REQUEST_AUTH_LOG_VALIDATION: false,
  // Add computed properties that are required
  MCP_ENDPOINT_URL: 'http://localhost:8080/mcp',
  OAUTH2_METADATA_URL: 'http://localhost:8080/.well-known/oauth-protected-resource',
  OAUTH2_AUTHORIZATION_URL: 'http://localhost:8080/authorize',
  OAUTH2_TOKEN_URL: 'http://localhost:8080/token',
  OAUTH2_REVOCATION_URL: 'http://localhost:8080/revoke',
  HEALTH_ENDPOINT_URL: 'http://localhost:8080/health',
  ...overrides,
});

describe('Environment Utilities', () => {
  describe('convertConfigTypes', () => {
    it('should convert boolean strings to booleans', () => {
      const input = {
        OAUTH2_ENABLED: 'true',
        CONNECTION_AUTH_ENABLED: 'false',
        DEBUG_SERVICE_ADAPTER: 'true',
      };

      const result = convertConfigTypes(input);

      expect(result.OAUTH2_ENABLED).toBe(true);
      expect(result.CONNECTION_AUTH_ENABLED).toBe(false);
      expect(result.DEBUG_SERVICE_ADAPTER).toBe(true);
    });

    it('should default undefined boolean fields to false', () => {
      const input = {
        someOtherField: 'value',
      };

      const result = convertConfigTypes(input);

      expect(result.OAUTH2_ENABLED).toBe(false);
      expect(result.CONNECTION_AUTH_ENABLED).toBe(false);
      expect(result.DEBUG_SERVICE_ADAPTER).toBe(false);
    });

    it('should preserve existing boolean values', () => {
      const input = {
        OAUTH2_ENABLED: true,
        CONNECTION_AUTH_ENABLED: false,
      };

      const result = convertConfigTypes(input);

      expect(result.OAUTH2_ENABLED).toBe(true);
      expect(result.CONNECTION_AUTH_ENABLED).toBe(false);
    });

    it('should handle removed OAUTH2_SCOPES (now client-provided)', () => {
      const input = {
        OAUTH2_SCOPES: 'openid, profile, email',
      };

      const result = convertConfigTypes(input);

      // OAUTH2_SCOPES conversion removed - now provided by clients in requests
      expect(result.OAUTH2_SCOPES).toEqual('openid, profile, email');
    });

    it('should preserve existing array for OAUTH2_SCOPES', () => {
      const input = {
        OAUTH2_SCOPES: ['openid', 'profile'],
      };

      const result = convertConfigTypes(input);

      expect(result.OAUTH2_SCOPES).toEqual(['openid', 'profile']);
    });

    it('should convert port strings to numbers', () => {
      const input = {
        MCP_SERVER_PORT: '8080',
        PER_REQUEST_AUTH_CACHE_MAX_AGE: '300',
      };

      const result = convertConfigTypes(input);

      expect(result.MCP_SERVER_PORT).toBe(8080);
      expect(result.PER_REQUEST_AUTH_CACHE_MAX_AGE).toBe(300);
    });

    it('should preserve existing numbers', () => {
      const input = {
        MCP_SERVER_PORT: 8080,
        PER_REQUEST_AUTH_CACHE_MAX_AGE: 300,
      };

      const result = convertConfigTypes(input);

      expect(result.MCP_SERVER_PORT).toBe(8080);
      expect(result.PER_REQUEST_AUTH_CACHE_MAX_AGE).toBe(300);
    });

    it('should not modify the original config object', () => {
      const input = {
        OAUTH2_ENABLED: 'true',
        MCP_SERVER_PORT: '8080',
      };
      const originalInput = { ...input };

      convertConfigTypes(input);

      expect(input).toEqual(originalInput);
    });

    it('should handle empty config object', () => {
      const result = convertConfigTypes({});

      expect(result.OAUTH2_ENABLED).toBe(false);
      expect(result.CONNECTION_AUTH_ENABLED).toBe(false);
      expect(typeof result).toBe('object');
    });
  });

  describe('validateEnvironmentWithNacos', () => {
    it('should validate complete valid configuration', () => {
      const config = createMockConfig();
      const result = validateEnvironmentWithNacos(config);

      expect(result.valid).toBe(true);
      expect(result.missing).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should require AUTH_COOKIES when OAuth2 is disabled', () => {
      const config = createMockConfig({
        AUTH_COOKIES: '',
        OAUTH2_ENABLED: false,
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.valid).toBe(false);
      expect(result.missing).toContain('AUTH_COOKIES (required when OAuth2 is disabled)');
    });

    it('should require VITE_API_HOST_KONG', () => {
      const config = createMockConfig({
        VITE_API_HOST_KONG: '',
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.valid).toBe(false);
      expect(result.missing).toContain('VITE_API_HOST_KONG');
    });

    it('should require OAuth2 config when OAuth2 is enabled', () => {
      const config = createMockConfig({
        OAUTH2_ENABLED: true,
        // Note: OAUTH2_CLIENT_ID removed - now provided by clients in requests
        KEYCLOAK_BASE_URL: '',
        KEYCLOAK_REALM: '',
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.valid).toBe(false);
      // Note: OAUTH2_CLIENT_ID no longer required - provided by clients in requests
      expect(result.missing).toContain('KEYCLOAK_BASE_URL');
      expect(result.missing).toContain('KEYCLOAK_REALM');
    });

    it('should validate URLs format', () => {
      const config = createMockConfig({
        VITE_API_HOST: 'invalid-url',
        KEYCLOAK_BASE_URL: 'also-invalid',
        OAUTH2_ENABLED: true,
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.valid).toBe(false);
      expect(result.missing.some(m => m.includes('VITE_API_HOST'))).toBe(true);
      expect(result.missing.some(m => m.includes('KEYCLOAK_BASE_URL'))).toBe(true);
    });

    it('should validate port numbers', () => {
      const config = createMockConfig({
        MCP_SERVER_PORT: 99999, // Port above 65535 would be invalid, but 99999 is actually valid
      });
      const result = validateEnvironmentWithNacos(config);

      // 99999 is within valid range (1024-65535), so no warning expected
      expect(result.warnings.some(w => w.includes('port'))).toBe(false);
    });

    it('should warn about invalid port numbers', () => {
      const config = createMockConfig({
        MCP_SERVER_PORT: 100, // Port below 1024
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.warnings.some(w => w.includes('MCP_SERVER_PORT'))).toBe(true);
    });

    it('should handle production environment with debug enabled', () => {
      const config = createMockConfig({
        NODE_ENV: 'production',
        DEBUG_SERVICE_ADAPTER: true,
      });
      const result = validateEnvironmentWithNacos(config);

      expect(result.security).toContain('DEBUG_SERVICE_ADAPTER should be false in production');
    });

    it('should handle OAuth2 client auth method validation', () => {
      const config = createMockConfig({
        OAUTH2_ENABLED: true,
        OAUTH2_CLIENT_AUTH_METHOD: 'none',
        OAUTH2_CLIENT_SECRET: 'should-not-be-required',
      });
      const result = validateEnvironmentWithNacos(config);

      // For 'none' auth method, client secret warnings should be less strict
      expect(result.valid).toBe(true);
    });
  });

  describe('logEnvironmentInfoWithNacos', () => {
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    it('should log environment configuration without exposing secrets', () => {
      const config = createMockConfig({
        NODE_ENV: 'test',
        TRANSPORT: 'http',
        VITE_API_HOST: 'https://api.example.com',
        VITE_API_HOST_KONG: 'https://kong.example.com',
        VITE_MASTER_DATA_API_HOST: 'https://master.example.com',
        VITE_MASTER_DATA_API_KEY: 'secret-key',
        AUTH_COOKIES: 'secret-cookies',
        OAUTH2_ENABLED: true,
        OAUTH2_CLIENT_SECRET: 'secret-client-secret',
        KEYCLOAK_BASE_URL: 'https://keycloak.example.com',
        KEYCLOAK_REALM: 'test',
        OAUTH2_CLIENT_AUTH_METHOD: 'client_secret_basic',
        MCP_SERVER_PORT: 8080,
        MCP_ENDPOINT_URL: 'http://localhost:8080/mcp',
        OAUTH2_METADATA_URL: 'http://localhost:8080/.well-known/oauth-protected-resource',
        OAUTH2_AUTHORIZATION_URL: 'http://localhost:8080/authorize',
        OAUTH2_TOKEN_URL: 'http://localhost:8080/token',
        OAUTH2_REVOCATION_URL: 'http://localhost:8080/revoke',
      });

      logEnvironmentInfoWithNacos(config);

      // Check that configuration is logged
      expect(consoleSpy).toHaveBeenCalledWith('🌍 Environment Configuration (with NACOS):');
      expect(consoleSpy).toHaveBeenCalledWith('  NODE_ENV: test');
      expect(consoleSpy).toHaveBeenCalledWith('  TRANSPORT: http');
      expect(consoleSpy).toHaveBeenCalledWith('  VITE_API_HOST: https://api.example.com');

      // Check that secrets are masked
      expect(consoleSpy).toHaveBeenCalledWith('  VITE_MASTER_DATA_API_KEY: ***SET***');
      expect(consoleSpy).toHaveBeenCalledWith('  AUTH_COOKIES: ***SET***');
      expect(consoleSpy).toHaveBeenCalledWith('  OAUTH2_CLIENT_SECRET: ***SET***');

      // Ensure secrets are not logged in plain text
      const allCalls = consoleSpy.mock.calls.flat();
      expect(allCalls.some(call => call.includes('secret-key'))).toBe(false);
      expect(allCalls.some(call => call.includes('secret-cookies'))).toBe(false);
      expect(allCalls.some(call => call.includes('secret-client-secret'))).toBe(false);
    });

    it('should show NOT SET for missing secrets', () => {
      const config = createMockConfig({
        VITE_MASTER_DATA_API_KEY: '',
        AUTH_COOKIES: '',
        OAUTH2_ENABLED: true, // Enable OAuth2 so client secret is logged
        OAUTH2_CLIENT_SECRET: '',
      });

      logEnvironmentInfoWithNacos(config);

      expect(consoleSpy).toHaveBeenCalledWith('  VITE_MASTER_DATA_API_KEY: ***NOT SET***');
      expect(consoleSpy).toHaveBeenCalledWith('  AUTH_COOKIES: ***NOT SET***');
      expect(consoleSpy).toHaveBeenCalledWith('  OAUTH2_CLIENT_SECRET: ***NOT SET***');
    });
  });
});
