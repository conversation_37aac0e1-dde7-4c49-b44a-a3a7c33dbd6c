# MCP Server ODI

A Model Context Protocol (MCP) server providing access to ODI services through standardized MCP tools.

## 🚀 Quick Start

```bash
# 1. Install dependencies
npm install

# 2. Configure authentication
cp .env.example .env
# Edit .env and set AUTH_COOKIES

# 3. Build and start
npm run build
npm start

# 4. Test with MCP Inspector
npm run inspect
```

## 🛠️ Available Tools

### 📦 OMS (Order Management) - 7 Tools

- `oms_queryOrderLists` - Query orders with pagination/filters
- `oms_getOrderDetail` - Get detailed order information
- `oms_getLogisticsInfo` - Get order logistics information
- `oms_getAssemblingInfo` - Get order assembling information
- `oms_searchPermissionStore` - Search user permission stores
- `oms_getRetrievalData` - Get order retrieval data
- `oms_queryStoreSubsidyAuditData` - Query store subsidy audit data

### 🌐 Global Services - 1 Tool

- `global_getCurrentUser` - Get current user information

### 🧪 Testing Tools - 2 Tools

- `test_ping` - Quick connectivity test
- `test_echo` - Echo test for debugging

## 📋 Requirements

- **Node.js** 18+
- **Valid authentication cookies** in `.env` file

## 🔧 Development

```bash
# Development mode with auto-reload
npm run dev

# Testing
npm test             # Unit tests (guidance)
make test-fast       # Integration tests (quick, ~20s)
make test-full       # Integration tests (complete, ~2-5min)

# MCP Inspector (interactive testing)
npm run inspect       # Development mode
npm run inspect:build # Production build mode


```

## 📊 Authentication

Set authentication cookies in `.env`:

```bash
AUTH_COOKIES=session_id=abc123; auth_token=xyz789
```

Get cookies from your browser's developer tools when logged into ODI Web.

## 🏗️ Project Structure

```
mcp-server/
├── src/
│   ├── adapters/     # Service implementations
│   ├── auth/         # OAuth2 authentication (optional)
│   ├── transport/    # HTTP/stdio transport
│   ├── utils/        # Utilities
│   └── server.ts     # MCP server setup
├── blackbox/         # Blackbox testing suite
├── docs/             # Documentation
└── dist/             # Compiled output
```

## 📚 Documentation

- [Testing Guide](docs/TESTING_GUIDE.md) - Comprehensive testing suite (blackbox, OAuth2, Docker)
- [OAuth2 Guide](docs/OAUTH2_GUIDE.md) - OAuth2 authentication setup and advanced features
- [Environment Guide](docs/ENVIRONMENT_GUIDE.md) - Environment configuration and NACOS integration
- [API Reference](docs/api.md) - Tool parameters and responses
- [Security Best Practices](docs/SECURITY_BEST_PRACTICES.md) - Security guidelines

## 🚨 Troubleshooting

**401 Errors**: Update `AUTH_COOKIES` in `.env` file
**Tool Not Found**: Run `npm run build` first
**Slow Responses**: Check network connectivity to API endpoints

## 📄 License

MIT License - see LICENSE file for details.
