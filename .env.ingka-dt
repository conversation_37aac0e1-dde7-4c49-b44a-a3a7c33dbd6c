# ================================
# MCP Server ODI - INGKA-DT Environment
# ================================

# Environment
NODE_ENV=development

# API Endpoints (Required)
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

# Authentication (Required when OAuth2 disabled)
AUTH_COOKIES=test_orders-portal=NDIzMTAwZTgtMWMwZC00YTAwLWExxxxxxxxxxxx
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/odi/oms/index

# Transport & Debug
TRANSPORT=stdio
DEBUG_SERVICE_ADAPTER=false

# MCP Server Port
MCP_SERVER_PORT=8080

# ================================
# OAuth2 Configuration
# ================================
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-mpc-odi
OAUTH2_CLIENT_SECRET=JHULZUN7XUxaFxwSrq2BSsta0nbpEmt1
OAUTH2_SCOPES=openid,profile,email