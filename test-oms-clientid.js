#!/usr/bin/env node

/**
 * Simple test to trigger OMS service call and verify orders-portal clientId usage
 */

import fetch from 'node-fetch';
import fs from 'fs';

const MCP_SERVER_URL = 'http://localhost:6300/mcp';
const SESSION_ID = 'test-oms-' + Math.random().toString(36).substring(2, 15);

// Load the real JWT token
const tokenData = JSON.parse(fs.readFileSync('./local-only/token.ingka-dt.cn.json', 'utf8'));
const JWT_TOKEN = tokenData.access_token;

async function testOMSClientId() {
  console.log('🏪 Testing OMS Service Call with orders-portal ClientId');
  console.log('='.repeat(60));
  console.log(`📡 Server: ${MCP_SERVER_URL}`);
  console.log(`🔑 Session: ${SESSION_ID}`);
  console.log('');

  try {
    // Make OMS service call directly (this should trigger service-specific clientId)
    console.log('🏪 Making OMS Service Call...');
    console.log('   Expected: clientId = orders-portal');
    console.log('   Check server logs for permission service interaction');

    const omsResponse = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${JWT_TOKEN}`,
        'X-Session-ID': SESSION_ID,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'oms_queryOrderLists',
          arguments: { page: 1, pageSize: 5 },
        },
      }),
    });

    console.log(`📊 Response Status: ${omsResponse.status}`);
    console.log(`📊 Response Headers:`, Object.fromEntries(omsResponse.headers.entries()));

    const responseText = await omsResponse.text();
    console.log(`📊 Response Body (first 500 chars):`, responseText.substring(0, 500));

    console.log('');
    console.log('🔍 Check server logs for:');
    console.log('   - "Using service-specific clientId for token exchange"');
    console.log('   - "serviceSpecificClientId: orders-portal"');
    console.log('   - "Permission service HTTP request" with clientId: orders-portal');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testOMSClientId().catch(console.error);
