name: <PERSON><PERSON> Manually # The name of your workflow
on: # The name of the GitHub event that triggers the workflow
  # When using the 'workflow_dispatch' event, you can optionally specify inputs that are passed to the workflow.
  workflow_dispatch:
    inputs:
      deploy_env:
        description: 'The environment you gonna deploy your service in(dev/prod)'
        required: true
jobs:
  build-and-push-image:
    # Name of the job which will be shown on the web console
    name: Build & Push Docker Image and Deploy
    # Use a list of labels to determine which runner the workflow will run on
    runs-on:
      - self-hosted
      - ubuntu-lts
      - ali

    # Global environment variables
    env:
      RT_CDH_REGISTRY: "artifactory.cloud.ingka-system.cn"
      PRODUCT_NAME: "mcp"   # put your production name here

    steps:
      - name: Hint
        run: |
          echo "Deploy Environment is ${{ github.event.inputs.deploy_env }} with branch ${GITHUB_REF#refs/heads/}"
      # Checks-out your repository under $GITHUB_WORKSPACE on runner, so your workflow can access it
      - uses: ikea-github-actions/checkout@v2

      # Login IKEA's artifactory
      - name: Login to Artifactory
        uses: ikea-github-actions/login-action@v1
        with:
          registry: artifactory.cloud.ingka-system.cn
          username: ${{ secrets.RT_CDH_USERNAME }}
          password: ${{ secrets.RT_CDH_TOKEN }}

      - name: Download Docker Buildx
        run: |
          mkdir -p $HOME/.docker/cli-plugins \
          && curl https://ikea-tools.oss-cn-shanghai.aliyuncs.com/docker-buildx/buildx-v0.5.1.linux-amd64 -o $HOME/.docker/cli-plugins/docker-buildx \
          && chmod +x $HOME/.docker/cli-plugins/docker-buildx

      # Setup Docker context for Buildx
      - name: Set up Docker Context
        run: |
          docker context rm builder | true
          docker context create builder
        continue-on-error: true

      # This action will create and boot a builder that can be used in the following steps of the workflow
      - name: Set up Docker Buildx
        uses: ikea-github-actions/setup-buildx-action@v1
        with:
          endpoint: builder
          driver-opts: image=artifactory.cloud.ingka-system.cn/cn-digital-hub-docker-virtual/aid-base/buildkit:buildx-stable-1

      # Get the environment variable ready
      - name: Set env
        run: |
          _branch_name="${GITHUB_REF#refs/heads/}"
          _branch_name="${_branch_name//\//-}"
          # Generate the image tag, e.g:[branch-name(escape the slash)]-[build-time(second level)]-[short-commit-id]
          TagName=$_branch_name-$(TZ=UTC-8 date '+%Y%m%d%H%M%S')-${GITHUB_SHA::8}
          SERVICE_IMAGE_NAME=${GITHUB_REPOSITORY#*/}
          # Get the namespce of artifactory based on the 'deploy_env' you input at the very beginning
          if [[ ${{ github.event.inputs.deploy_env }} == "prod" || ${{ github.event.inputs.deploy_env }} == "stg" ]]; then
              RT_CDH_NAMESPACE="cn-digital-hub-docker-release-local"
              K8S_MANIFEST_REPO=china-digital-hub/odi-deploy-prod
              K8S_MANIFEST_FILEPATH=${{ github.event.inputs.deploy_env }}/mcp/mcp-server-odi/values.yaml
          else
              RT_CDH_NAMESPACE="cn-digital-hub-docker-dev-local"
              K8S_MANIFEST_REPO=china-digital-hub/orders-deploy-test
              K8S_MANIFEST_FILEPATH=${{ github.event.inputs.deploy_env }}/mcp/mcp-server-odi/values.yaml
          fi
          echo IMAGE_FULL_NAME="${{ env.RT_CDH_REGISTRY }}/${RT_CDH_NAMESPACE}/${{ env.PRODUCT_NAME }}/${SERVICE_IMAGE_NAME}:${TagName}" >> $GITHUB_ENV
          echo IMAGE_PULL_NAME="${{ env.RT_CDH_REGISTRY }}/cn-digital-hub-docker-virtual/${{ env.PRODUCT_NAME }}/${SERVICE_IMAGE_NAME}:${TagName}" >> $GITHUB_ENV
          echo "K8S_MANIFEST_REPO=${K8S_MANIFEST_REPO}" >> $GITHUB_ENV
          echo "K8S_MANIFEST_FILEPATH=${K8S_MANIFEST_FILEPATH}" >> $GITHUB_ENV
          echo "TagName=${TagName}" >> $GITHUB_ENV

      # To build and push Docker image with Buildx with full support of the features provided by Moby BuildKit builder toolkit
      - name: Build and push
        # id: A unique identifier for the step. You can use the id to reference the step in contexts
        id: docker_build
        uses: IKEA-Github-Actions/build-push-action@v4.1.1
        with:
          # push: Push the build result to registry, default: false
          push: true
          # Do not use cache when building the image
          no-cache: true
          tags: "${{ env.IMAGE_FULL_NAME }}"

      # Print the image digest at the end of the workflow
      - name: Image name
        run: echo ${{ env.IMAGE_FULL_NAME }}

      - name: Update Image Version in the related HelmChart values.yaml
        uses: china-digital-hub/shared-workflows/actions/bump-image-tag@master
        with:
          valueFile: ${{ env.K8S_MANIFEST_FILEPATH }}
          value: ${{ env.TagName }}
          repository: ${{ env.K8S_MANIFEST_REPO }}
          githubToken: ${{ secrets.GIT_TOKEN_DEPLOY_TEST }}
          autoMerge: true
          propertyPath: '.imageTag'