# 🔧 Environment Configuration Guide

**Complete guide for configuring MCP Server ODI environment variables across all deployment scenarios.**

## 📋 Configuration Hierarchy

Environment variables are loaded in this order (later values override earlier ones):

1. **Application Defaults** (Code fallbacks)
2. **Dockerfile ENV** (Container defaults)
3. **Kubernetes Environment** (Helm values)
4. **NACOS Configuration** (Production control - **HIGHEST PRIORITY**)

## 🚀 Quick Start

### 1. Copy Template

```bash
# Copy the example file
cp .env.example .env

# Edit with your values
nano .env
```

### 2. Validate Configuration

```bash
# Validate your environment setup
npm run validate:env

# Start development server (includes validation)
npm run dev
```

### 3. Test Configuration

```bash
# Test with MCP Inspector
npm run inspect

# Run quick tests
npm run test
```

## 📊 Variable Categories

### Infrastructure Variables (K8s/Docker Only)

These variables control infrastructure and should **NOT** be managed via NACOS:

| Variable              | Default                 | Description         | Source |
| --------------------- | ----------------------- | ------------------- | ------ |
| `NODE_ENV`            | `development`           | Runtime environment | K8s    |
| `MCP_SERVER_PORT`     | `8080`                  | Container port      | K8s    |
| `MCP_SERVER_BASE_URL` | `http://localhost:8080` | Service base URL    | K8s    |
| `TRANSPORT`           | `http`                  | Transport protocol  | K8s    |

### NACOS Configuration Variables

These variables control NACOS integration:

| Variable              | Default                           | Description               | Source     |
| --------------------- | --------------------------------- | ------------------------- | ---------- |
| `NACOS_SERVER_ADDR`   | -                                 | NACOS server address      | K8s        |
| `NACOS_NAMESPACE`     | -                                 | NACOS namespace           | K8s        |
| `NACOS_ACCESS_KEY`    | -                                 | NACOS access key          | K8s Secret |
| `NACOS_SECRET_KEY`    | -                                 | NACOS secret key          | K8s Secret |
| `NACOS_DATA_ID`       | `com.ikea.mpp:mcp-server-odi-dev` | Configuration data ID     | K8s        |
| `NACOS_GROUP`         | `DEFAULT_GROUP`                   | Configuration group       | K8s        |
| `NACOS_CONFIG_FORMAT` | `yaml`                            | Config format (yaml/json) | K8s        |

### Application Configuration (NACOS Controlled)

These variables are managed via NACOS for production flexibility:

| Variable                       | Default                                            | Description               | NACOS Control |
| ------------------------------ | -------------------------------------------------- | ------------------------- | ------------- |
| `VITE_API_HOST`                | `https://api-dev-i.ingka-dt.cn`                    | Primary API endpoint      | ✅            |
| `VITE_API_HOST_KONG`           | `https://api-dev-i.ingka-dt.cn`                    | Kong gateway endpoint     | ✅            |
| `VITE_MASTER_DATA_API_HOST`    | `https://master-data-api-dev.ingka-dt.cn`          | Master data API           | ✅            |
| `VITE_MASTER_DATA_API_KEY`     | ``                                                 | Master data API key       | ✅            |
| `AUTH_COOKIES`                 | ``                                                 | Session authentication    | ✅            |
| `X_CUSTOM_REFERRER`            | `https://fe-dev-i.ingka-dt.cn/order-web`           | Request referrer          | ✅            |
| `PERMISSION_SERVICE_BASE_URL`  | `https://mpp-dev-i.ingka-dt.cn/permission-service` | Permission service URL    | ✅            |
| `PERMISSION_SERVICE_CLIENT_ID` | `mcp-mpc-odi`                                      | Permission service client | ✅            |

### Feature Flags (NACOS Controlled)

Runtime behavior toggles managed via NACOS:

| Variable                          | Default | Description               | NACOS Control |
| --------------------------------- | ------- | ------------------------- | ------------- |
| `OAUTH2_ENABLED`                  | `false` | OAuth2 authentication     | ✅            |
| `CONNECTION_AUTH_ENABLED`         | `false` | Connection-level auth     | ✅            |
| `CONNECTION_AUTH_STRICT`          | `false` | Strict auth mode          | ✅            |
| `CONNECTION_AUTH_TEST_API`        | `false` | Enable test API endpoints | ✅            |
| `DEBUG_SERVICE_ADAPTER`           | `false` | Debug logging             | ✅            |
| `DEBUG_OAUTH2`                    | `false` | OAuth2 debug logging      | ✅            |
| `PER_REQUEST_AUTH_ENABLED`        | `false` | Per-request validation    | ✅            |
| `PER_REQUEST_AUTH_CACHE_ENABLED`  | `true`  | Auth result caching       | ✅            |
| `PER_REQUEST_AUTH_CACHE_MAX_AGE`  | `300`   | Cache TTL (seconds)       | ✅            |
| `PER_REQUEST_AUTH_LOG_VALIDATION` | `false` | Log auth validation       | ✅            |

### OAuth2 Configuration (NACOS + K8s)

OAuth2 and Keycloak integration:

| Variable                          | Default                        | Description          | Source     |
| --------------------------------- | ------------------------------ | -------------------- | ---------- |
| `KEYCLOAK_BASE_URL`               | `https://keycloak.ingka-dt.cn` | Keycloak server      | NACOS      |
| `KEYCLOAK_REALM`                  | `master`                       | Keycloak realm       | NACOS      |
| `OAUTH2_CLIENT_ID`                | `mcp-server`                   | OAuth2 client ID     | NACOS      |
| `OAUTH2_CLIENT_SECRET`            | ``                             | OAuth2 client secret | K8s Secret |
| `OAUTH2_CLIENT_AUTH_METHOD`       | `client_secret_basic`          | Auth method          | NACOS      |
| `OAUTH2_ADMIN_SCOPES`             | `admin,roles`                  | Admin scopes         | NACOS      |
| `OAUTH2_WRITE_SCOPES`             | `write`                        | Write scopes         | NACOS      |
| `OAUTH2_DEFAULT_SCOPE_VALIDATION` | `any`                          | Scope validation     | NACOS      |

## 🏠 Local Development

### Example `.env` for Development

```env
# Infrastructure Variables
NODE_ENV=development
TRANSPORT=http
MCP_SERVER_PORT=6300
MCP_SERVER_BASE_URL=http://localhost:6300

# Application Configuration
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=

# Authentication
AUTH_COOKIES=
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# Permission Service
PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
PERMISSION_SERVICE_CLIENT_ID=orders-portal

# Feature Flags
OAUTH2_ENABLED=true
CONNECTION_AUTH_ENABLED=false
CONNECTION_AUTH_STRICT=false
DEBUG_SERVICE_ADAPTER=true

# OAuth2 Configuration
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-mpc-odi
OAUTH2_CLIENT_AUTH_METHOD=none
```

## 🚀 Production Configuration

### NACOS Configuration (Production)

```yaml
# Feature Flags (Production)
OAUTH2_ENABLED: true
CONNECTION_AUTH_ENABLED: true
CONNECTION_AUTH_STRICT: true
CONNECTION_AUTH_TEST_API: false
DEBUG_SERVICE_ADAPTER: false

# API Endpoints (Production)
VITE_API_HOST: 'https://admin.ingka-internal.cn/app-api/orders-portal/prod'
VITE_API_HOST_KONG: 'https://api-prod-mpp-fe.ingka-internal.cn'

# OAuth2 Configuration (Production)
KEYCLOAK_BASE_URL: 'https://keycloak.ingka-internal.cn/auth'
OAUTH2_CLIENT_ID: 'mcp-server-prod'
```

### Kubernetes Helm Values (Production)

```yaml
environments:
  # Infrastructure (K8s Only)
  - name: NODE_ENV
    value: 'production'
  - name: MCP_SERVER_PORT
    value: '8080'
  - name: MCP_SERVER_BASE_URL
    value: 'https://mcp.ingka-internal.cn'

  # NACOS Connection (K8s Only)
  - name: NACOS_SERVER_ADDR
    value: 'mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848'
  - name: NACOS_NAMESPACE
    value: '8bc453ff-d8df-4ee3-acf4-4de86c19f955'

secrets:
  # Sensitive Configuration (K8s Secrets)
  - name: NACOS_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: nacos-credentials
        key: access-key
  - name: NACOS_SECRET_KEY
    valueFrom:
      secretKeyRef:
        name: nacos-credentials
        key: secret-key
  - name: OAUTH2_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: oauth2-credentials
        key: client-secret
```

## 🔍 Validation

### Validate Environment Variables

```bash
# Validate environment variables
node scripts/validate-env.js

# Test NACOS connection
node scripts/nacos-config-manager.js test-connection

# Validate NACOS configuration
node scripts/nacos-config-manager.js validate
```

### Common Issues

1. **Missing required variables**: Check validation output
2. **NACOS connection failed**: Verify NACOS credentials
3. **OAuth2 errors**: Check Keycloak configuration
4. **API connection issues**: Verify endpoint URLs

## 🚨 Emergency Overrides

In case NACOS is unavailable, you can override critical settings via Helm values:

```yaml
environments:
  - name: OAUTH2_ENABLED
    value: 'false' # Emergency disable
  - name: CONNECTION_AUTH_ENABLED
    value: 'false' # Emergency disable
```

## 🔧 NACOS Integration

### NACOS Setup (Production)

NACOS provides centralized configuration management for production environments:

#### 1. Access Aliyun MSE Console

1. Log into your Aliyun account
2. Navigate to **MSE (Microservices Engine)**
3. Go to **NACOS** → **Configuration Management**

#### 2. Create Configuration

- **Data ID**: `com.ikea.mpp:mcp-server-odi-dev` (or `-prod`)
- **Group**: `DEFAULT_GROUP`
- **Namespace**: `8bc453ff-d8df-4ee3-acf4-4de86c19f955`
- **Configuration Format**: `YAML` (recommended)

#### 3. NACOS Configuration Example

```yaml
# Application Configuration (NACOS Controlled)
VITE_API_HOST: 'https://admin.ingka-dt.cn/app-api/orders-portal/uat'
VITE_API_HOST_KONG: 'https://fe-dev-i.ingka-dt.cn/order-web'
VITE_MASTER_DATA_API_HOST: 'https://admin.ingka-dt.cn/master-data'
VITE_MASTER_DATA_API_KEY: ''

# Authentication & Authorization
AUTH_COOKIES: ''
X_CUSTOM_REFERRER: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'

# OAuth2 Configuration
OAUTH2_ENABLED: true
KEYCLOAK_BASE_URL: 'https://keycloak.ingka-dt.cn/auth'
KEYCLOAK_REALM: 'master'
OAUTH2_CLIENT_ID: 'mcp-mpc-odi'
OAUTH2_CLIENT_SECRET: ''
OAUTH2_CLIENT_AUTH_METHOD: 'none'

# OAuth2 Scopes and Validation
OAUTH2_ADMIN_SCOPES: 'admin,roles'
OAUTH2_WRITE_SCOPES: 'write'
OAUTH2_DEFAULT_SCOPE_VALIDATION: 'any'

# Connection Authentication
CONNECTION_AUTH_ENABLED: true
CONNECTION_AUTH_STRICT: true
CONNECTION_AUTH_TEST_API: true

# Per-Request Authentication
PER_REQUEST_AUTH_ENABLED: true
PER_REQUEST_AUTH_CACHE_ENABLED: true
PER_REQUEST_AUTH_CACHE_MAX_AGE: 300
PER_REQUEST_AUTH_LOG_VALIDATION: false

# Feature Flags & Debug Configuration
DEBUG_SERVICE_ADAPTER: true
```

#### 4. NACOS Connection (K8s Secrets)

```yaml
# Kubernetes secrets for NACOS access
secrets:
  - name: NACOS_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: mse-access-id-order
        key: key
  - name: NACOS_SECRET_KEY
    valueFrom:
      secretKeyRef:
        name: mse-access-secret-order
        key: key

# NACOS connection environment variables
environments:
  - name: NACOS_SERVER_ADDR
    value: 'mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848'
  - name: NACOS_NAMESPACE
    value: '8bc453ff-d8df-4ee3-acf4-4de86c19f955'
  - name: NACOS_DATA_ID
    value: 'com.ikea.mpp:mcp-server-odi-dev'
  - name: NACOS_GROUP
    value: 'DEFAULT_GROUP'
  - name: NACOS_CONFIG_FORMAT
    value: 'yaml'
```

### NACOS Testing

Test NACOS integration:

```bash
# Test NACOS connection
node scripts/nacos-config-manager.js test-connection

# Validate NACOS configuration
node scripts/nacos-config-manager.js validate

# Load configuration from NACOS
node scripts/nacos-config-manager.js load
```

## 📚 Related Documentation

- `.env.example` - Template file
- `scripts/validate-env.js` - Validation script
- `scripts/nacos-config-manager.js` - NACOS management script
- `docs/SECURITY_BEST_PRACTICES.md` - Security guidelines
