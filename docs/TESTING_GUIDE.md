# 🧪 Testing Guide

**Comprehensive testing guide for MCP Server ODI including blackbox testing, OAuth2 validation, and Docker-based testing.**

## 🚀 Quick Start

### Daily Development (Recommended)

```bash
# Quick OAuth validation (30s) - BEST FOR DAILY USE
make test-oauth-quick      # OAuth ENABLED server
make test-oauth-disabled   # OAuth DISABLED server

# Fast blackbox test (20s) - Core MCP functionality
make test-fast

# Unit tests only (no server needed)
npm run test:unit
```

### Comprehensive Testing

```bash
# Full OAuth test suite (1-2min)
make test-oauth

# Complete blackbox test suite (2-5min)
make test-full

# All integration tests (3-4min)
make test-integration
```

## 🔐 OAuth Testing Suite

### Core OAuth Tests

1. **OAuth Flow Test** - Complete OAuth 2.0 Authorization Code flow with PKCE
2. **OAuth Failure Test** - Authentication failure scenarios and error handling
3. **OAuth Endpoints Test** - OAuth endpoint validation and parameter testing
4. **OAuth Config Test** - Configuration and environment variable testing
5. **OAuth Streamable Test** - OAuth integration with StreamableHTTP transport

### OAuth Test Commands

```bash
# Quick OAuth validation (recommended for daily use)
make test-oauth-quick

# OAuth disabled server testing
make test-oauth-disabled

# Complete OAuth test suite
make test-oauth

# OAuth-specific test runner
./blackbox/scripts/run-oauth-tests.sh
```

## 🐳 Docker-Based Testing

### Enhanced Docker Testing

Comprehensive Docker-based testing with multiple server configurations:

```bash
# Enhanced Docker testing (recommended)
./blackbox/scripts/run-enhanced-tests.sh

# Or using npm
npm run test:blackbox:enhanced
```

### Test Configurations

- **OAuth2 Disabled Server** (Port 3000) - No authentication required
- **OAuth2 Enabled Server** (Port 3001) - Full OAuth2 authentication
- **Connection Auth Server** (Port 3002) - Connection-level authentication

### What Docker Tests Cover

- ✅ OAuth2 enabled/disabled modes
- ✅ OAuth2 discovery endpoint validation
- ✅ Token validation (valid/invalid/missing)
- ✅ WWW-Authenticate header compliance
- ✅ Streamable HTTP transport validation
- ✅ Server-Sent Events response format
- ✅ Connection auth modes (enabled/disabled/strict)
- ✅ Error scenarios and edge cases

## 📊 Test Categories

### 1. Protocol Compliance Tests

- MCP protocol specification compliance
- Request/response format validation
- Transport layer testing (HTTP/stdio)
- Content negotiation and headers

### 2. Authentication Tests

- OAuth2 authorization code flow with PKCE
- Token validation and refresh
- Cookie-based authentication
- Authentication failure scenarios
- WWW-Authenticate header compliance

### 3. Functional Tests

- All MCP tools functionality
- OMS (Order Management) tools
- Global services tools
- Test/debugging tools
- Error handling and edge cases

### 4. Performance Tests

- Load testing and benchmarking
- Response time validation
- Memory usage monitoring
- Concurrent connection handling

### 5. Security Tests

- Authentication bypass attempts
- Invalid token handling
- Malformed request handling
- Rate limiting validation

## 🔧 Test Configuration

### Environment Setup

Tests use your current `.env` configuration with these key variables:

```bash
# Required for OAuth testing
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
OAUTH2_CLIENT_ID=mcp-mpc-odi

# Required API endpoints
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web

# Authentication cookies (required even when auth disabled)
AUTH_COOKIES=test_orders-portal=<cookie-value>
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# Permission service
PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
PERMISSION_SERVICE_CLIENT_ID=orders-portal
```

### Docker Test Configuration

Docker tests use `blackbox/config/docker-compose.enhanced.yml` with:

- Automatic server lifecycle management
- Clean isolation between test runs
- Multiple server configurations
- Comprehensive logging and reporting

## 📁 Test Structure

### Blackbox Tests Directory

```
blackbox/
├── tests/
│   ├── oauth-flow-test.js           # Complete OAuth 2.0 flow
│   ├── oauth-failure-test.js        # OAuth failure scenarios
│   ├── oauth-endpoints-test.js      # OAuth endpoint validation
│   ├── oauth-config-test.js         # OAuth configuration testing
│   ├── oauth-streamable-test.js     # OAuth + StreamableHTTP
│   ├── oauth-server-blackbox-test.js # Pure blackbox OAuth testing
│   ├── protocol-compliance-test.js  # MCP protocol compliance
│   ├── docker-test-runner.js        # Docker test orchestrator
│   └── quick-auth-test.js           # Quick authentication validation
├── config/
│   └── docker-compose.enhanced.yml  # Enhanced Docker configuration
├── scripts/
│   ├── run-enhanced-tests.sh        # Enhanced Docker testing
│   └── run-oauth-tests.sh           # OAuth-specific testing
└── results/                         # Test results and reports
```

## 🎯 Test Results and Reporting

### Success Criteria

- **OAuth Enabled Server**: 100% success rate (8/8 tests)
- **OAuth Disabled Server**: 100% success rate (8/8 tests)
- **Protocol Compliance**: All MCP protocol tests pass
- **Functional Tests**: All tools return valid responses

### Test Reports

Tests generate detailed reports in `blackbox/results/`:

- `docker-test-report-*.json` - Detailed test results
- `failure-analysis-*.json` - Failure analysis and debugging info
- Test logs and performance metrics

### Monitoring Dashboard

Real-time test monitoring available:

```bash
# Start monitoring dashboard
node blackbox/tests/status-dashboard.js
```

## 🚨 Troubleshooting

### Common Issues

1. **OAuth Tests Failing**
   - Check Keycloak connectivity: `npm run keycloak:test`
   - Verify OAuth2 client configuration
   - Ensure OAUTH2_ENABLED=true in .env

2. **Docker Tests Failing**
   - Check Docker daemon is running
   - Verify port availability (3000, 3001, 3002)
   - Clean up previous containers: `docker-compose down -v`

3. **Authentication Errors**
   - Update AUTH_COOKIES in .env file
   - Check API endpoint connectivity
   - Verify permission service configuration

4. **Performance Issues**
   - Check network connectivity to API endpoints
   - Monitor server resource usage
   - Review test timeout configurations

### Debug Commands

```bash
# Validate environment configuration
npm run validate:env

# Test individual components
npm run keycloak:test
curl http://localhost:3000/health

# Check Docker containers
docker ps
docker logs <container-name>

# Clean up test environment
./blackbox/scripts/run-enhanced-tests.sh cleanup
```

## 📈 Performance Benchmarks

### Expected Performance

- **Quick tests**: Complete in <30 seconds
- **Full test suite**: Complete in <5 minutes
- **OAuth flow**: <2 seconds per complete flow
- **API response time**: <500ms average

### Monitoring Metrics

- Test execution time
- Success/failure rates
- API response times
- Memory usage during tests
- Docker container startup time

## 🔄 Continuous Integration

### CI/CD Integration

Tests are designed for CI/CD environments:

```yaml
# Example GitHub Actions
- name: Run Quick Tests
  run: make test-oauth-quick

- name: Run Full Test Suite
  run: make test-full

- name: Enhanced Docker Tests
  run: ./blackbox/scripts/run-enhanced-tests.sh
```

### Test Automation

- Automatic cleanup after test runs
- Parallel test execution support
- Detailed logging for debugging
- Exit codes for CI/CD integration

## 📚 Related Documentation

- `docs/OAUTH2_GUIDE.md` - OAuth2 authentication setup
- `docs/ENVIRONMENT_GUIDE.md` - Environment configuration
- `docs/SECURITY_BEST_PRACTICES.md` - Security guidelines
- `docs/api.md` - API reference and tool documentation
