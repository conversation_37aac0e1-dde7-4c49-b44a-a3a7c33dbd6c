# 🔒 Security Best Practices

This document outlines security best practices for the MCP Server ODI deployment and configuration.

## 🔐 Environment Variable Security

### 1. Secret Management
- **Never commit `.env` files** to version control
- Use **environment-specific** configuration files (`.env.production`, `.env.staging`)
- Store secrets in **secure secret management systems** (<PERSON><PERSON><PERSON><PERSON><PERSON> Vault, AWS Secrets Manager, etc.)
- **Rotate secrets regularly** (API keys, OAuth2 client secrets, etc.)

### 2. Environment Validation
The server performs automatic validation on startup:
- ✅ **Required variables** are present
- ⚠️  **Warnings** for potential misconfigurations
- 🔒 **Security recommendations** for production environments

### 3. Production Security Checklist
```bash
# ✅ Required for production
NODE_ENV=production
DEBUG_SERVICE_ADAPTER=false
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
OAUTH2_ENABLED=true

# ✅ Use HTTPS URLs only
KEYCLOAK_BASE_URL=https://keycloak.your-domain.com
MCP_SERVER_BASE_URL=https://mcp.your-domain.com
VITE_API_HOST=https://api.your-domain.com

# ✅ Strong, unique secrets
OAUTH2_CLIENT_SECRET=<strong-random-secret>
VITE_MASTER_DATA_API_KEY=<unique-api-key>
```

## 🌐 Network Security

### 1. Transport Security
- **HTTP Transport**: Use HTTPS with valid TLS certificates
- **Stdio Transport**: Secure the host system and process isolation
- **Port Configuration**: Use non-privileged ports (1024-65535)

### 2. API Endpoint Security
- All API endpoints should use **HTTPS**
- Validate **SSL certificates** in production
- Use **internal networks** when possible for service-to-service communication

## 🔑 Authentication & Authorization

### 1. OAuth2 Configuration
```bash
# ✅ Production OAuth2 setup
OAUTH2_ENABLED=true
OAUTH2_CLIENT_ID=mcp-server-prod
OAUTH2_CLIENT_SECRET=<strong-secret>
KEYCLOAK_BASE_URL=https://keycloak.internal.com
KEYCLOAK_REALM=production
```

### 2. Connection Authentication
```bash
# ✅ Strict authentication for production
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
CONNECTION_AUTH_TEST_API=false
```

### 3. Per-Request Authentication
```bash
# ✅ MCP compliance with caching
PER_REQUEST_AUTH_ENABLED=true
PER_REQUEST_AUTH_CACHE_ENABLED=true
PER_REQUEST_AUTH_CACHE_MAX_AGE=300
PER_REQUEST_AUTH_LOG_VALIDATION=false
```

## 🐛 Debug & Logging Security

### 1. Production Logging
```bash
# ✅ Disable debug features in production
DEBUG_SERVICE_ADAPTER=false
DEBUG_HTTP_REQUESTS=false
DEBUG_OAUTH2=false
PER_REQUEST_AUTH_LOG_VALIDATION=false
```

### 2. Log Security
- **Never log sensitive data** (tokens, passwords, API keys)
- Use **structured logging** with appropriate log levels
- **Rotate logs** regularly and securely store them
- **Monitor logs** for security events and anomalies

## 🚀 Deployment Security

### 1. Container Security
```dockerfile
# Use non-root user
USER node

# Set secure environment
ENV NODE_ENV=production
ENV DEBUG_SERVICE_ADAPTER=false
```

### 2. Docker Compose Security
```yaml
services:
  mcp-server:
    # Use secrets for sensitive data
    secrets:
      - oauth2_client_secret
      - master_data_api_key
    
    # Limit container capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
```

## 🔍 Security Monitoring

### 1. Health Checks
- Monitor authentication failures
- Track API response times and errors
- Alert on unusual access patterns

### 2. Security Headers
```javascript
// Add security headers for HTTP transport
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});
```

## 🚨 Incident Response

### 1. Secret Compromise
1. **Immediately rotate** compromised secrets
2. **Revoke access tokens** if OAuth2 is compromised
3. **Update all environments** with new secrets
4. **Monitor logs** for unauthorized access

### 2. Security Updates
- **Regularly update** dependencies
- **Monitor security advisories** for Node.js and npm packages
- **Test security updates** in staging before production

## 📋 Security Checklist

### Pre-Deployment
- [ ] All secrets are stored securely (not in code)
- [ ] Production environment variables are validated
- [ ] Debug features are disabled
- [ ] HTTPS is enforced for all endpoints
- [ ] Authentication is properly configured
- [ ] Security headers are implemented

### Post-Deployment
- [ ] Monitor authentication logs
- [ ] Verify SSL certificate validity
- [ ] Test OAuth2 flow end-to-end
- [ ] Confirm debug endpoints are disabled
- [ ] Validate API access controls

### Regular Maintenance
- [ ] Rotate secrets quarterly
- [ ] Update dependencies monthly
- [ ] Review access logs weekly
- [ ] Test backup/recovery procedures
- [ ] Audit user permissions

## 📚 Additional Resources

- [OWASP Security Guidelines](https://owasp.org/)
- [Node.js Security Best Practices](https://nodejs.org/en/docs/guides/security/)
- [OAuth2 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)
- [Docker Security Best Practices](https://docs.docker.com/engine/security/)
