# 🔐 OAuth2 Authentication Guide

**Complete guide for OAuth2 authentication with Keycloak integration, advanced features, and production deployment.**

## 🚀 Quick Start

### Option 1: Configuration Scripts (Recommended)

**For public clients (mcp-inspector):**

```bash
npm run oauth:public
```

**For confidential clients (production):**

```bash
npm run oauth:confidential
```

**Check current configuration:**

```bash
npm run oauth:status
```

### Option 2: Manual Configuration

**Enable OAuth2** in `.env`:

```bash
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master

# For confidential clients (production)
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret
OAUTH2_CLIENT_AUTH_METHOD=client_secret_basic

# For public clients (development/testing)
OAUTH2_CLIENT_ID=your-public-client-id
OAUTH2_CLIENT_SECRET=
OAUTH2_CLIENT_AUTH_METHOD=none
```

**Test OAuth2:**

```bash
npm run keycloak:test
npm run oauth2:dev
```

## 🏗️ Authentication Architecture

### Components Flow

```
MCP Inspector → MCP Server → Order Service
     (A)           (B)           (C)
```

- **A → B**: Keycloak OAuth2 Authentication (JWT Bearer token)
- **B**: JWT Token Local Validation (signature, expiration, issuer)
- **B → C**: Cookie-based Authentication (session cookies)

### Authentication Methods

#### client_secret_basic (Production)

- **Use case**: Production servers, secure environments
- **Requirements**: Client secret must be configured
- **Security**: Highest - uses HTTP Basic authentication
- **RFC Compliance**: OAuth 2.1, RFC 6749

#### none (Public Clients)

- **Use case**: Development tools, mcp-inspector, CLI tools
- **Requirements**: No client secret, relies on PKCE for security
- **Security**: Suitable for public clients with PKCE
- **RFC Compliance**: OAuth 2.1, RFC 7636 (PKCE)

## 🔧 Advanced Features

### 1. Token Caching System

**Purpose**: Reduce Keycloak calls and improve performance

**Configuration:**

```typescript
const cache = new TokenCache({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 1000, // Max 1000 entries
  cleanupInterval: 60 * 1000, // Cleanup every minute
});
```

**Benefits:**

- Reduces Keycloak API calls by ~80-90%
- Cache hits respond in <1ms vs 50-200ms
- Configurable TTL and size limits

### 2. Scope-Based Authorization

**Validation Modes:**

- `all`: Requires ALL specified scopes (default)
- `any`: Requires ANY of the specified scopes
- `custom`: Use custom validation function

**Usage:**

```typescript
import { requireReadAccess, requireWriteAccess, requireAdminAccess } from './auth/middleware.js';

// Require specific scopes
app.get('/api/data', requireReadAccess(provider), handler);
app.post('/api/data', requireWriteAccess(provider), handler);
app.delete('/api/data', requireAdminAccess(provider), handler);
```

**Expected Scopes:**

- `read`: For read-only access
- `write`: For write access
- `admin`: For administrative access
- `openid`: Required for all authenticated requests

### 3. Comprehensive Logging

**Event Types:**

- `authorization_request`
- `authorization_success`
- `authorization_failure`
- `token_validation`
- `cache_hit` / `cache_miss`
- `scope_validation`

**Usage:**

```typescript
import { globalOAuth2Logger } from './auth/oauth-logger.js';

// Get recent events
const events = globalOAuth2Logger.getEvents(100);

// Get metrics
const metrics = globalOAuth2Logger.getMetrics();
```

## 🌐 Production Deployment

### Keycloak Client Setup

1. Login to Keycloak Admin Console
2. Create new client with your CLIENT_ID
3. Enable Client Authentication
4. Set Valid Redirect URIs: `https://your-domain.com/*`
5. Copy Client Secret to environment variables

### Nginx Proxy Configuration

Add these rules for OAuth2 discovery:

```nginx
# Handle domain root OAuth2 discovery (MCP Inspector fallback)
location /.well-known/oauth-protected-resource {
    proxy_pass http://mcp-server-backend/mcp-server-odi/.well-known/oauth-protected-resource;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /.well-known/oauth-authorization-server {
    proxy_pass http://mcp-server-backend/mcp-server-odi/.well-known/oauth-authorization-server;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Handle weird URL patterns (MCP SDK compatibility)
location ~ ^/.well-known/oauth-protected-resource/(.+)$ {
    proxy_pass http://mcp-server-backend/$1/.well-known/oauth-protected-resource;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### Production Environment Variables

```bash
# Production Security Settings
NODE_ENV=production
OAUTH2_ENABLED=true
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
CONNECTION_AUTH_TEST_API=false
DEBUG_SERVICE_ADAPTER=false

# OAuth2 Configuration
KEYCLOAK_BASE_URL=https://keycloak.ingka-internal.cn/auth
OAUTH2_CLIENT_ID=mcp-server-prod
OAUTH2_CLIENT_SECRET=<strong-random-secret>
OAUTH2_CLIENT_AUTH_METHOD=client_secret_basic

# Use HTTPS URLs only
MCP_SERVER_BASE_URL=https://mcp.ingka-internal.cn
```

## 🔍 API Endpoints

### Cache Management

- `GET /auth/cache/status` - Get cache status (requires read access)
- `POST /auth/cache/clear` - Clear cache (requires admin access)
- `DELETE /auth/cache/:token` - Remove specific token (requires admin access)

### Logging and Monitoring

- `GET /auth/logs/events?limit=100` - Get recent events (requires read access)
- `GET /auth/logs/metrics` - Get metrics (requires read access)
- `GET /auth/logs/events/:type` - Get events by type (requires read access)
- `POST /auth/logs/clear` - Clear logs (requires admin access)

## 🧪 Testing

### Test OAuth2 Discovery

```bash
# All these should return 200 OK:
curl https://your-domain.com/mcp-server-odi/.well-known/oauth-protected-resource
curl https://your-domain.com/.well-known/oauth-protected-resource
curl https://your-domain.com/.well-known/oauth-authorization-server
```

### OAuth2 Scripts

- `oauth:public` - Switch to public client mode
- `oauth:confidential` - Switch to confidential client mode
- `oauth:status` - Show current OAuth2 configuration
- `keycloak:test` - Test Keycloak connectivity
- `oauth2:dev` - Start server with OAuth2 enabled

## 🔒 Security Best Practices

1. **Production Settings**: Always use `client_secret_basic` with strong secrets
2. **HTTPS Only**: All URLs must use HTTPS in production
3. **Secret Management**: Store secrets in secure secret management systems
4. **Token Validation**: Use local JWT validation, not introspection
5. **Scope Design**: Use hierarchical scopes (read < write < admin)
6. **Monitoring**: Set up alerts for cache hit rate and error rates

## 📊 Performance Monitoring

### Key Metrics

- **Cache Hit Rate**: Should be >80% in steady state
- **Authorization Failures**: Monitor for sudden spikes
- **Token Validation Latency**: Should be <100ms average
- **Error Rates**: By error type and client

### Monitoring Commands

```bash
# Get cache performance
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/auth/cache/status

# Get OAuth2 metrics
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/auth/logs/metrics
```

## 🚨 Troubleshooting

### Common Issues

1. **High Cache Miss Rate**: Check token TTL configuration
2. **Scope Validation Failures**: Enable logging and check required vs actual scopes
3. **Discovery Issues**: Verify nginx proxy configuration
4. **Performance Issues**: Monitor cache hit rate and response times

### Debug Mode

Enable detailed logging:

```typescript
const middleware = createOAuth2Middleware(provider, {
  enableLogging: true,
  requiredScopes: ['read'],
});
```

## 📚 Related Documentation

- `docs/ENVIRONMENT_GUIDE.md` - Environment configuration
- `docs/SECURITY_BEST_PRACTICES.md` - Security guidelines
- `scripts/validate-env.js` - Environment validation
