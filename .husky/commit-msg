#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "📝 Validating commit message..."

# Read the commit message
commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
  echo "❌ Invalid commit message format!"
  echo ""
  echo "📋 Commit message should follow conventional commits format:"
  echo "   <type>[optional scope]: <description>"
  echo ""
  echo "🏷️  Valid types:"
  echo "   feat:     A new feature"
  echo "   fix:      A bug fix"
  echo "   docs:     Documentation only changes"
  echo "   style:    Changes that do not affect the meaning of the code"
  echo "   refactor: A code change that neither fixes a bug nor adds a feature"
  echo "   test:     Adding missing tests or correcting existing tests"
  echo "   chore:    Changes to the build process or auxiliary tools"
  echo "   perf:     A code change that improves performance"
  echo "   ci:       Changes to CI configuration files and scripts"
  echo "   build:    Changes that affect the build system or external dependencies"
  echo "   revert:   Reverts a previous commit"
  echo ""
  echo "📝 Examples:"
  echo "   feat: add OAuth2 authentication"
  echo "   fix(auth): resolve token validation issue"
  echo "   docs: update API documentation"
  echo "   test: add blackbox tests for MCP protocol"
  echo ""
  exit 1
fi

echo "✅ Commit message format is valid!"
