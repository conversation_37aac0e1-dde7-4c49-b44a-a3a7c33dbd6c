#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# 1. Type check (CRITICAL - must pass)
echo "🔍 Running TypeScript type check..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
  echo "❌ TypeScript type check failed. Please fix type errors before committing."
  exit 1
fi

# 2. Build check (CRITICAL - must pass)
echo "🏗️ Testing build..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed. Please fix build errors before committing."
  exit 1
fi

# 3. Lint and format staged files (CRITICAL - must pass)
echo "📝 Running lint-staged (ESLint + Prettier)..."
npx lint-staged
if [ $? -ne 0 ]; then
  echo "❌ Linting/formatting failed. Please fix issues before committing."
  exit 1
fi

# 4. Run tests (WARNING - show issues but don't block for now)
echo "🧪 Running tests..."
npm test || {
  echo "⚠️ Tests failed or not implemented. Consider adding/fixing tests."
  # Continue despite test issues
}

echo "✅ Critical pre-commit checks passed!"
