# after edit

1. please commit code with less than 3 lines of commit message
2. never bypass pre-commit errors
3. push code, and use gh cli to trigger build, which will build image and deploy to dev environment
4. usually takes 10 min for it to take effect, then you can use argocd commandline to check the deployment status
5. for dev env, you can use commandline like `curl -s https://mpp-dev-i.ingka-dt.cn/mcp-server-odi/health | jq '{status, version}'` to check if the latest version is alive, the version contains commit id prefix
