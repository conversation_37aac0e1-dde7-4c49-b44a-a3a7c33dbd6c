#!/bin/bash

# OAuth Tests Runner
# Runs comprehensive OAuth test suite for MCP server
# 
# Usage:
#   ./blackbox/scripts/run-oauth-tests.sh [oauth_enabled_url] [oauth_disabled_url]
#
# Environment variables:
#   MCP_SERVER_OAUTH2_ENABLED_URL  - URL for OAuth-enabled server (default: http://localhost:3001)
#   MCP_SERVER_OAUTH2_DISABLED_URL - URL for OAuth-disabled server (default: http://localhost:3000)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default URLs
OAUTH_ENABLED_URL=${1:-${MCP_SERVER_OAUTH2_ENABLED_URL:-"http://localhost:3001"}}
OAUTH_DISABLED_URL=${2:-${MCP_SERVER_OAUTH2_DISABLED_URL:-"http://localhost:3000"}}

echo -e "${BOLD}${CYAN}🔐 OAuth Test Suite Runner${NC}"
echo -e "${BLUE}OAuth Enabled Server: ${OAUTH_ENABLED_URL}${NC}"
echo -e "${BLUE}OAuth Disabled Server: ${OAUTH_DISABLED_URL}${NC}"
echo "============================================================"

# Change to project root
cd "$(dirname "$0")/../.."

# Create results directory
mkdir -p blackbox/results

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TIMESTAMP=$(date +%s)
RESULTS_FILE="blackbox/results/oauth-test-report-${TIMESTAMP}.json"

# Function to run a test and capture results
run_test() {
    local test_name="$1"
    local test_script="$2"
    local server_url="$3"
    local extra_env="$4"
    
    echo -e "\n${CYAN}🧪 Running ${test_name}...${NC}"
    
    local start_time=$(date +%s)
    local test_output
    local test_exit_code
    
    # Run the test with timeout
    if timeout 120s env MCP_SERVER_URL="$server_url" $extra_env node "$test_script" > /tmp/test_output.log 2>&1; then
        test_exit_code=0
        test_output=$(cat /tmp/test_output.log)
        echo -e "${GREEN}✅ ${test_name} PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        test_exit_code=$?
        test_output=$(cat /tmp/test_output.log)
        echo -e "${RED}❌ ${test_name} FAILED (exit code: $test_exit_code)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        # Show last few lines of output for debugging
        echo -e "${YELLOW}Last 10 lines of output:${NC}"
        tail -10 /tmp/test_output.log | sed 's/^/  /'
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Store test result
    cat >> "$RESULTS_FILE" << EOF
{
  "test": "$test_name",
  "script": "$test_script",
  "server_url": "$server_url",
  "status": $([ $test_exit_code -eq 0 ] && echo '"PASS"' || echo '"FAIL"'),
  "exit_code": $test_exit_code,
  "duration_ms": $duration,
  "timestamp": "$(date -Iseconds)",
  "output": $(echo "$test_output" | jq -Rs .)
},
EOF
}

# Function to check if server is running
check_server() {
    local url="$1"
    local name="$2"
    
    echo -e "${BLUE}🔍 Checking ${name} server at ${url}...${NC}"
    
    if curl -s --max-time 10 "${url}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ ${name} server is running${NC}"
        return 0
    else
        echo -e "${RED}❌ ${name} server is not responding${NC}"
        return 1
    fi
}

# Start JSON results file
cat > "$RESULTS_FILE" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "oauth_enabled_url": "$OAUTH_ENABLED_URL",
  "oauth_disabled_url": "$OAUTH_DISABLED_URL",
  "test_results": [
EOF

echo -e "\n${BOLD}🏥 Health Checks${NC}"
echo "============================================================"

# Check if servers are running
OAUTH_ENABLED_RUNNING=false
OAUTH_DISABLED_RUNNING=false

if check_server "$OAUTH_ENABLED_URL" "OAuth Enabled"; then
    OAUTH_ENABLED_RUNNING=true
fi

if check_server "$OAUTH_DISABLED_URL" "OAuth Disabled"; then
    OAUTH_DISABLED_RUNNING=true
fi

if [ "$OAUTH_ENABLED_RUNNING" = false ] && [ "$OAUTH_DISABLED_RUNNING" = false ]; then
    echo -e "${RED}❌ No servers are running. Please start the MCP servers first.${NC}"
    exit 1
fi

echo -e "\n${BOLD}🧪 Running OAuth Tests${NC}"
echo "============================================================"

# Test 1: OAuth Configuration Test (both servers)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "OAuth Config Test (Enabled)" "blackbox/tests/oauth-config-test.js" "$OAUTH_ENABLED_URL"
fi

if [ "$OAUTH_DISABLED_RUNNING" = true ]; then
    run_test "OAuth Config Test (Disabled)" "blackbox/tests/oauth-config-test.js" "$OAUTH_DISABLED_URL"
fi

# Test 2: OAuth Endpoints Test (OAuth enabled server only)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "OAuth Endpoints Test" "blackbox/tests/oauth-endpoints-test.js" "$OAUTH_ENABLED_URL"
fi

# Test 3: OAuth Flow Test (OAuth enabled server only)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "OAuth Flow Test" "blackbox/tests/oauth-flow-test.js" "$OAUTH_ENABLED_URL"
fi

# Test 4: OAuth Failure Test (OAuth enabled server only)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "OAuth Failure Test" "blackbox/tests/oauth-failure-test.js" "$OAUTH_ENABLED_URL"
fi

# Test 5: OAuth Streamable Test (OAuth enabled server only)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "OAuth Streamable Test" "blackbox/tests/oauth-streamable-test.js" "$OAUTH_ENABLED_URL"
fi

# Test 6: Quick Auth Test (both servers)
if [ "$OAUTH_ENABLED_RUNNING" = true ]; then
    run_test "Quick Auth Test (Enabled)" "blackbox/tests/quick-auth-test.js" "$OAUTH_ENABLED_URL"
fi

if [ "$OAUTH_DISABLED_RUNNING" = true ]; then
    run_test "Quick Auth Test (Disabled)" "blackbox/tests/quick-auth-test.js" "$OAUTH_DISABLED_URL"
fi

# Close JSON results file
sed -i '$ s/,$//' "$RESULTS_FILE"  # Remove last comma
cat >> "$RESULTS_FILE" << EOF
  ],
  "summary": {
    "total_tests": $TOTAL_TESTS,
    "passed_tests": $PASSED_TESTS,
    "failed_tests": $FAILED_TESTS,
    "success_rate": $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)
  }
}
EOF

# Print summary
echo -e "\n${BOLD}📊 Test Summary${NC}"
echo "============================================================"
echo -e "${BLUE}Total Tests: ${TOTAL_TESTS}${NC}"
echo -e "${GREEN}Passed: ${PASSED_TESTS}${NC}"
echo -e "${RED}Failed: ${FAILED_TESTS}${NC}"

SUCCESS_RATE=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)
if (( $(echo "$SUCCESS_RATE >= 90" | bc -l) )); then
    echo -e "${GREEN}Success Rate: ${SUCCESS_RATE}%${NC}"
elif (( $(echo "$SUCCESS_RATE >= 70" | bc -l) )); then
    echo -e "${YELLOW}Success Rate: ${SUCCESS_RATE}%${NC}"
else
    echo -e "${RED}Success Rate: ${SUCCESS_RATE}%${NC}"
fi

echo -e "\n${BLUE}📄 Results saved to: ${RESULTS_FILE}${NC}"

# Clean up
rm -f /tmp/test_output.log

# Exit with appropriate code
if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All OAuth tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some OAuth tests failed. Check the results for details.${NC}"
    exit 1
fi
