#!/bin/bash

# ================================
# 🔧 NACOS Test Environment Setup Script
# ================================
#
# This script helps set up NACOS configuration for blackbox testing
#
# Usage:
#   ./blackbox/scripts/setup-nacos-test.sh
#   ./blackbox/scripts/setup-nacos-test.sh --validate
#   ./blackbox/scripts/setup-nacos-test.sh --help

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONFIG_DIR="$PROJECT_ROOT/blackbox/config"

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}🔧 NACOS Test Environment Setup${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_help() {
    print_header
    echo "Setup NACOS configuration for blackbox testing"
    echo ""
    echo "Usage:"
    echo "  $0                 Setup test environment configuration"
    echo "  $0 --validate      Validate current test configuration"
    echo "  $0 --help          Show this help message"
    echo ""
    echo "Files managed:"
    echo "  blackbox/config/.env.test        Template configuration"
    echo "  blackbox/config/.env.test.local  Local overrides (gitignored)"
    echo ""
    echo "Environment variables used:"
    echo "  MSE_ACCESS_KEY_TEST              Test MSE access key"
    echo "  MSE_ACCESS_SECRET_TEST           Test MSE access secret"
    echo "  NACOS_SERVER_ADDR_TEST           Test NACOS server address"
    echo "  NACOS_NAMESPACE_TEST             Test NACOS namespace"
    echo ""
}

setup_test_config() {
    print_header
    print_info "Setting up NACOS test environment configuration..."
    
    # Check if .env.test exists
    if [[ ! -f "$CONFIG_DIR/.env.test" ]]; then
        print_error ".env.test template not found at $CONFIG_DIR/.env.test"
        exit 1
    fi
    
    # Create .env.test.local if it doesn't exist
    if [[ ! -f "$CONFIG_DIR/.env.test.local" ]]; then
        print_info "Creating .env.test.local from template..."
        cp "$CONFIG_DIR/.env.test" "$CONFIG_DIR/.env.test.local"
        print_success "Created $CONFIG_DIR/.env.test.local"
        print_warning "Please update .env.test.local with your actual test credentials!"
    else
        print_info ".env.test.local already exists"
    fi
    
    # Check if credentials are still default values
    if grep -q "test-access-key-replace-with-actual" "$CONFIG_DIR/.env.test.local" 2>/dev/null; then
        print_warning "Default test credentials detected in .env.test.local"
        print_warning "Please update with actual test credentials before running tests"
    fi
    
    print_success "Test environment configuration setup complete!"
    echo ""
    print_info "Next steps:"
    echo "  1. Edit $CONFIG_DIR/.env.test.local with actual test credentials"
    echo "  2. Run: make test-fast (quick test)"
    echo "  3. Run: make test-full (complete test suite)"
}

validate_config() {
    print_header
    print_info "Validating NACOS test configuration..."
    
    local errors=0
    
    # Check if .env.test.local exists
    if [[ ! -f "$CONFIG_DIR/.env.test.local" ]]; then
        print_error ".env.test.local not found. Run setup first."
        ((errors++))
    else
        print_success ".env.test.local exists"
        
        # Check for required variables
        local required_vars=(
            "MSE_ACCESS_KEY_TEST"
            "MSE_ACCESS_SECRET_TEST"
            "NACOS_SERVER_ADDR_TEST"
            "NACOS_NAMESPACE_TEST"
        )
        
        for var in "${required_vars[@]}"; do
            if grep -q "^${var}=" "$CONFIG_DIR/.env.test.local"; then
                print_success "$var is configured"
            else
                print_error "$var is missing from .env.test.local"
                ((errors++))
            fi
        done
        
        # Check for default values that should be replaced
        if grep -q "test-access-key-replace-with-actual" "$CONFIG_DIR/.env.test.local"; then
            print_warning "MSE_ACCESS_KEY_TEST still has default value"
            ((errors++))
        fi
        
        if grep -q "test-access-secret-replace-with-actual" "$CONFIG_DIR/.env.test.local"; then
            print_warning "MSE_ACCESS_SECRET_TEST still has default value"
            ((errors++))
        fi
    fi
    
    # Check Docker Compose file
    if [[ -f "$CONFIG_DIR/docker-compose.test.yml" ]]; then
        print_success "docker-compose.test.yml exists"
        
        # Check if NACOS variables are referenced
        if grep -q "NACOS_SERVER_ADDR" "$CONFIG_DIR/docker-compose.test.yml"; then
            print_success "NACOS configuration found in docker-compose.test.yml"
        else
            print_error "NACOS configuration missing from docker-compose.test.yml"
            ((errors++))
        fi
    else
        print_error "docker-compose.test.yml not found"
        ((errors++))
    fi
    
    echo ""
    if [[ $errors -eq 0 ]]; then
        print_success "Configuration validation passed! ✨"
        print_info "You can now run: make test-fast or make test-full"
    else
        print_error "Configuration validation failed with $errors error(s)"
        print_info "Run: $0 to setup configuration"
        exit 1
    fi
}

# Main script logic
case "${1:-setup}" in
    "--help"|"-h"|"help")
        show_help
        ;;
    "--validate"|"-v"|"validate")
        validate_config
        ;;
    "setup"|"")
        setup_test_config
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
