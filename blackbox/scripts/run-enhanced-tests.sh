#!/bin/bash

# 🚀 Enhanced Blackbox Test Runner for OAuth2 & Streamable HTTP Testing
# 
# Comprehensive Docker-based testing for:
# - OAuth2 enabled/disabled modes with current .env configs
# - MCP client-server streamable HTTP connections  
# - Authentication failure scenarios
# - Connection auth mode testing
#
# Usage:
#   ./blackbox/scripts/run-enhanced-tests.sh [command]
#
# Commands:
#   test     - Run enhanced test suite (default)
#   build    - Build Docker images only
#   clean    - Clean up Docker resources
#   logs     - Show service logs
#   status   - Show service status

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="blackbox/config/docker-compose.enhanced.yml"
PROJECT_NAME="mcp-enhanced-blackbox"
TIMEOUT=300 # 5 minutes

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${BOLD}${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build Docker images
build_images() {
    log_header "🏗️  Building Docker Images"
    
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" build --no-cache
    
    log_success "Docker images built successfully"
}

# Run enhanced test suite
run_tests() {
    log_header "🧪 Running Enhanced Blackbox Tests"
    
    # Clean up any existing containers
    cleanup_containers
    
    # Start services and run tests
    log_info "Starting test environment..."
    
    # Use timeout to prevent hanging
    timeout $TIMEOUT docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up --build --abort-on-container-exit --exit-code-from enhanced-test-runner
    
    local exit_code=$?
    
    # Collect logs regardless of test outcome
    collect_logs
    
    if [ $exit_code -eq 0 ]; then
        log_success "Enhanced blackbox tests completed successfully!"
    else
        log_error "Enhanced blackbox tests failed (exit code: $exit_code)"
    fi
    
    # Cleanup
    cleanup_containers
    
    return $exit_code
}

# Collect service logs
collect_logs() {
    log_info "Collecting service logs..."
    
    local log_dir="blackbox/results/logs"
    mkdir -p "$log_dir"
    
    # Collect logs from all services
    local services=("mcp-server-oauth2-disabled" "mcp-server-oauth2-enabled" "mcp-server-connection-auth" "enhanced-test-runner")
    
    for service in "${services[@]}"; do
        local log_file="$log_dir/${service}-$(date +%s).log"
        docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs "$service" > "$log_file" 2>&1 || true
        log_info "Logs saved: $log_file"
    done
}

# Show service logs
show_logs() {
    log_header "📋 Service Logs"
    
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs --follow
}

# Show service status
show_status() {
    log_header "📊 Service Status"
    
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps
}

# Clean up Docker resources
cleanup_containers() {
    log_info "Cleaning up Docker containers..."
    
    # Stop and remove containers
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down -v --remove-orphans 2>/dev/null || true
    
    # Remove any dangling containers
    docker ps -a --filter "name=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker rm -f 2>/dev/null || true
}

# Full cleanup including images
full_cleanup() {
    log_header "🧹 Full Cleanup"
    
    cleanup_containers
    
    # Remove project images
    docker images --filter "label=com.docker.compose.project=${PROJECT_NAME}" --format "{{.ID}}" | xargs -r docker rmi -f 2>/dev/null || true
    
    # Clean up build cache
    docker builder prune -f 2>/dev/null || true
    
    log_success "Full cleanup completed"
}

# Show usage information
show_usage() {
    echo "Enhanced Blackbox Test Runner for OAuth2 & Streamable HTTP Testing"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  test     - Run enhanced test suite (default)"
    echo "  build    - Build Docker images only"
    echo "  clean    - Clean up Docker resources"
    echo "  cleanup  - Full cleanup including images"
    echo "  logs     - Show service logs"
    echo "  status   - Show service status"
    echo "  help     - Show this help message"
    echo ""
    echo "Test Scenarios:"
    echo "  • OAuth2 disabled mode with basic auth"
    echo "  • OAuth2 enabled mode with per-request auth"
    echo "  • Connection auth mode testing"
    echo "  • Streamable HTTP transport validation"
    echo "  • Authentication failure scenarios"
    echo "  • MCP protocol compliance"
    echo ""
    echo "Environment:"
    echo "  • Docker Compose file: $COMPOSE_FILE"
    echo "  • Project name: $PROJECT_NAME"
    echo "  • Timeout: ${TIMEOUT}s"
}

# Main execution
main() {
    local command="${1:-test}"
    
    case "$command" in
        "test")
            check_prerequisites
            run_tests
            ;;
        "build")
            check_prerequisites
            build_images
            ;;
        "clean")
            cleanup_containers
            ;;
        "cleanup")
            full_cleanup
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Handle script interruption
trap 'log_warning "Script interrupted, cleaning up..."; cleanup_containers; exit 130' INT TERM

# Run main function
main "$@"
