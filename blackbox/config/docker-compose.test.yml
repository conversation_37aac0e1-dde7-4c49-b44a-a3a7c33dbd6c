# 🧪 MCP Server ODI - Blackbox Test Environment
# Docker Compose configuration for comprehensive testing with .env configuration
#
# Usage:
#   make test-fast    # Quick test
#   make test-full    # Complete test suite
#   make clean        # Cleanup
#
# Environment Configuration:
#   - Copy blackbox/config/.env.test to .env.test.local
#   - Update .env.test.local with actual test credentials
#   - Docker Compose will load environment variables automatically



services:
  # OAuth2 disabled server - for basic MCP testing
  mcp-server-oauth2-disabled:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    env_file:
      - .env.test.local
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3000
      - OAUTH2_ENABLED=false
      - CONNECTION_AUTH_ENABLED=false
      - CONNECTION_AUTH_STRICT=false
      - PER_REQUEST_AUTH_ENABLED=false
      - PER_REQUEST_AUTH_CACHE_ENABLED=false
      - MCP_SERVER_PUBLIC_BASE_URL=http://localhost:3000

      # API Endpoints (from current .env)
      - VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
      - VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
      - VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
      - VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e

      # Authentication (required even when disabled)
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=
      - X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

      # Permission Service
      - PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
      - PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
    ports:
      - "3000:3000"
    command: ["node", "dist/index.js"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 5s
      timeout: 3s
      retries: 10

  # OAuth2 enabled server
  mcp-server-oauth2-enabled:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    env_file:
      - .env.test.local
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3001
      - OAUTH2_ENABLED=true
      - CONNECTION_AUTH_ENABLED=false
      - CONNECTION_AUTH_STRICT=false
      - PER_REQUEST_AUTH_ENABLED=true
      - PER_REQUEST_AUTH_CACHE_ENABLED=true
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=

      # OAuth2 Configuration (matching .env)
      - OAUTH2_CLIENT_SECRET=test-secret-for-testing
      - KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
      - KEYCLOAK_REALM=master
      - OAUTH2_CLIENT_ID=mcp-mpc-odi
      - OAUTH2_CLIENT_AUTH_METHOD=none
      - OAUTH2_REDIRECT_URI=http://localhost:3001/auth/callback
      - OAUTH2_SCOPES=openid,profile,email
      - MCP_SERVER_PUBLIC_BASE_URL=http://localhost:3001
      - OAUTH2_ADMIN_SCOPES=admin,roles
      - OAUTH2_WRITE_SCOPES=write
      - OAUTH2_DEFAULT_SCOPE_VALIDATION=any

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
      - DEBUG_OAUTH2=true
    ports:
      - "3001:3001"
    command: ["node", "dist/index.js"]

  # Test runner
  test-runner:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Test Runner Configuration
      - NODE_ENV=test
      - MCP_SERVER_OAUTH2_DISABLED_URL=http://mcp-server-oauth2-disabled:3000
      - MCP_SERVER_OAUTH2_ENABLED_URL=http://mcp-server-oauth2-enabled:3001
    volumes:
      - ../:/app/blackbox:ro
      - ../results:/app/blackbox/results
    depends_on:
      - mcp-server-oauth2-disabled
      - mcp-server-oauth2-enabled
    command: ["node", "blackbox/tests/docker-test-runner.js"]
