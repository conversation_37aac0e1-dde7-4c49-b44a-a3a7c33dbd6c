# 🧪 Enhanced MCP Server ODI - Blackbox Test Environment
# Docker Compose configuration for comprehensive OAuth2 and Streamable HTTP testing
#
# Usage:
#   docker-compose -f blackbox/config/docker-compose.enhanced.yml up --build
#   docker-compose -f blackbox/config/docker-compose.enhanced.yml down -v
#
# Test Scenarios:
#   1. OAuth2 Disabled + Basic Auth (port 3000)
#   2. OAuth2 Enabled + Per-Request Auth (port 3001) 
#   3. Connection Auth Enabled (port 3002)
#   4. Streamable HTTP Testing (all modes)

version: '3.8'

services:
  # OAuth2 disabled server - for basic MCP testing
  mcp-server-oauth2-disabled:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3000
      - OAUTH2_ENABLED=false
      - CONNECTION_AUTH_ENABLED=false
      - PER_REQUEST_AUTH_ENABLED=false
      
      # API Endpoints (from current .env)
      - VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
      - VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
      - VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
      - VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e
      
      # Authentication (required even when disabled)
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmQTg1NmMxLjM2MDA=
      - X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index
      
      # Permission Service
      - PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
      - PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
    ports:
      - "3000:3000"
    command: ["node", "dist/index.js"]
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 5s
      timeout: 3s
      retries: 2
      start_period: 10s

  # OAuth2 enabled server - for OAuth2 authentication testing
  mcp-server-oauth2-enabled:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3001
      - OAUTH2_ENABLED=true
      - CONNECTION_AUTH_ENABLED=false
      - PER_REQUEST_AUTH_ENABLED=true
      
      # API Endpoints (from current .env)
      - VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
      - VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
      - VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
      - VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e
      
      # Authentication
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmQTg1NmMxLjM2MDA=
      - X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

      # OAuth2 Configuration (from current .env)
      - KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
      - KEYCLOAK_REALM=master
      - OAUTH2_CLIENT_ID=mcp-mpc-odi
      - OAUTH2_CLIENT_AUTH_METHOD=none
      - OAUTH2_ADMIN_SCOPES=admin,roles
      - OAUTH2_WRITE_SCOPES=write
      - OAUTH2_DEFAULT_SCOPE_VALIDATION=any
      
      # Permission Service
      - PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
      - PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
      - DEBUG_OAUTH2=true
    ports:
      - "3001:3001"
    command: ["node", "dist/index.js"]
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 5s
      timeout: 3s
      retries: 2
      start_period: 10s

  # Connection Auth enabled server - for connection-level authentication testing
  mcp-server-connection-auth:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3002
      - OAUTH2_ENABLED=false
      - CONNECTION_AUTH_ENABLED=true
      - CONNECTION_AUTH_STRICT=true
      - PER_REQUEST_AUTH_ENABLED=false
      
      # API Endpoints
      - VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
      - VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
      - VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
      - VITE_MASTER_DATA_API_KEY=TFucAHWAWLBXwfH6PZf7a2e
      
      # Authentication
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmQTg1NmMxLjM2MDA=
      - X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index
      
      # Permission Service
      - PERMISSION_SERVICE_BASE_URL=https://mpp-internal-fe.ingka-dt.cn/permission-service
      - PERMISSION_SERVICE_CLIENT_ID=mcp-mpc-odi

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=false
    ports:
      - "3002:3002"
    command: ["node", "dist/index.js"]
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 5s
      timeout: 3s
      retries: 2
      start_period: 10s

  # Enhanced test runner with comprehensive OAuth2 and streamable HTTP testing
  enhanced-test-runner:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Test Runner Configuration
      - NODE_ENV=test
      - MCP_SERVER_OAUTH2_DISABLED_URL=http://mcp-server-oauth2-disabled:3000
      - MCP_SERVER_OAUTH2_ENABLED_URL=http://mcp-server-oauth2-enabled:3001
      - MCP_SERVER_CONNECTION_AUTH_URL=http://mcp-server-connection-auth:3002
      
      # Test Configuration
      - TEST_TIMEOUT=30000
      - CONCURRENT_CLIENTS=5
      - TEST_DURATION=30
      - VERBOSE_LOGGING=true
    volumes:
      - ../:/app/blackbox:ro
      - ../results:/app/blackbox/results
    depends_on:
      - mcp-server-oauth2-disabled
      - mcp-server-oauth2-enabled
      - mcp-server-connection-auth
    command: ["node", "blackbox/tests/simple-docker-test.cjs"]

networks:
  default:
    name: mcp-blackbox-test-network
