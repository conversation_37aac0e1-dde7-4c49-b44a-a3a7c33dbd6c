#!/usr/bin/env node

/**
 * Quick Authentication Test
 *
 * Simple test to verify current authentication configuration
 *
 * Usage:
 *   node test/quick-auth-test.js
 *   npm run test:quick-auth
 */

import dotenv from 'dotenv';
import { resolve } from 'path';

// Load environment variables
dotenv.config({ path: resolve(process.cwd(), '.env') });

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });

    const text = await response.text();
    let data;
    try {
      data = JSON.parse(text);
    } catch {
      data = text;
    }

    return {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      data
    };
  } catch (error) {
    throw new Error(`Request failed: ${error.message}`);
  }
}

async function testCurrentConfiguration() {
  console.log(colorize('\n🧪 Quick Authentication Test', 'bold'));
  console.log('='.repeat(50));

  // Read port from environment or use default
  const port = process.env.MCP_SERVER_PORT || '6300';
  const baseUrl = `http://localhost:${port}`;
  let results = [];

  // Test 1: Health Check
  try {
    console.log(colorize('🔍 Testing health endpoint...', 'blue'));
    const response = await makeRequest(`${baseUrl}/health`);
    if (response.status === 200) {
      console.log(colorize('✅ Health endpoint: OK', 'green'));
      results.push({ test: 'Health', status: 'PASS' });
    } else {
      console.log(colorize(`❌ Health endpoint: ${response.status}`, 'red'));
      results.push({ test: 'Health', status: 'FAIL' });
    }
  } catch (error) {
    console.log(colorize(`❌ Health endpoint: ${error.message}`, 'red'));
    results.push({ test: 'Health', status: 'FAIL' });
  }

  // Test 2: Capabilities
  try {
    console.log(colorize('🔍 Testing capabilities endpoint...', 'blue'));
    const response = await makeRequest(`${baseUrl}/mcp/capabilities`);
    if (response.status === 200) {
      const oauth2Status = response.data.oauth2_enabled ? 'ENABLED' : 'DISABLED';
      console.log(colorize(`✅ Capabilities: OK (OAuth2: ${oauth2Status})`, 'green'));
      results.push({ test: 'Capabilities', status: 'PASS', oauth2: oauth2Status });
    } else {
      console.log(colorize(`❌ Capabilities: ${response.status}`, 'red'));
      results.push({ test: 'Capabilities', status: 'FAIL' });
    }
  } catch (error) {
    console.log(colorize(`❌ Capabilities: ${error.message}`, 'red'));
    results.push({ test: 'Capabilities', status: 'FAIL' });
  }

  // Test 3: Auth Status
  try {
    console.log(colorize('🔍 Testing auth status endpoint...', 'blue'));
    const response = await makeRequest(`${baseUrl}/auth/status`);
    if (response.status === 200) {
      const config = response.data.config;
      console.log(colorize('✅ Auth Status: OK', 'green'));
      console.log(colorize(`   Connection Auth: ${config.connectionAuthEnabled ? 'ENABLED' : 'DISABLED'}`, 'blue'));
      console.log(colorize(`   Per-Request Auth: ${config.perRequestAuthEnabled ? 'ENABLED' : 'DISABLED'}`, 'blue'));
      console.log(colorize(`   Auth Strict: ${config.connectionAuthStrict ? 'ENABLED' : 'DISABLED'}`, 'blue'));
      results.push({ test: 'Auth Status', status: 'PASS', config });
    } else {
      console.log(colorize(`❌ Auth Status: ${response.status}`, 'red'));
      results.push({ test: 'Auth Status', status: 'FAIL' });
    }
  } catch (error) {
    console.log(colorize(`❌ Auth Status: ${error.message}`, 'red'));
    results.push({ test: 'Auth Status', status: 'FAIL' });
  }

  // Test 4: OAuth2 Metadata (should be 404 if disabled)
  try {
    console.log(colorize('🔍 Testing OAuth2 metadata endpoint...', 'blue'));
    const response = await makeRequest(`${baseUrl}/.well-known/oauth-protected-resource`);
    if (response.status === 404) {
      console.log(colorize('✅ OAuth2 Metadata: Properly disabled (404)', 'green'));
      results.push({ test: 'OAuth2 Metadata', status: 'PASS', note: 'Disabled' });
    } else if (response.status === 200) {
      console.log(colorize('✅ OAuth2 Metadata: Available (200)', 'green'));
      results.push({ test: 'OAuth2 Metadata', status: 'PASS', note: 'Enabled' });
    } else {
      console.log(colorize(`❌ OAuth2 Metadata: Unexpected ${response.status}`, 'red'));
      results.push({ test: 'OAuth2 Metadata', status: 'FAIL' });
    }
  } catch (error) {
    console.log(colorize(`❌ OAuth2 Metadata: ${error.message}`, 'red'));
    results.push({ test: 'OAuth2 Metadata', status: 'FAIL' });
  }

  // Test 5: MCP Initialize (no auth)
  try {
    console.log(colorize('🔍 Testing MCP initialize without auth...', 'blue'));
    const response = await makeRequest(`${baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/event-stream'
      },
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' }
        }
      }
    });

    if (response.status === 200) {
      console.log(colorize('✅ MCP Initialize: Works without auth', 'green'));
      results.push({ test: 'MCP Initialize (no auth)', status: 'PASS', note: 'Auth disabled' });
    } else if (response.status === 401) {
      console.log(colorize('✅ MCP Initialize: Requires auth (401)', 'green'));
      results.push({ test: 'MCP Initialize (no auth)', status: 'PASS', note: 'Auth required' });
    } else {
      console.log(colorize(`❌ MCP Initialize: Unexpected ${response.status}`, 'red'));
      results.push({ test: 'MCP Initialize (no auth)', status: 'FAIL' });
    }
  } catch (error) {
    console.log(colorize(`❌ MCP Initialize: ${error.message}`, 'red'));
    results.push({ test: 'MCP Initialize (no auth)', status: 'FAIL' });
  }

  // Summary
  console.log(colorize('\n📊 Test Summary', 'bold'));
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.status === 'PASS').length;
  const total = results.length;
  
  console.log(colorize(`Tests Passed: ${passed}/${total}`, passed === total ? 'green' : 'yellow'));
  
  if (passed === total) {
    console.log(colorize('🎉 All tests passed! Server is working correctly.', 'green'));
  } else {
    console.log(colorize('⚠️  Some tests failed. Check server configuration.', 'yellow'));
  }

  // Configuration Summary
  const authConfig = results.find(r => r.test === 'Auth Status');
  const oauth2Config = results.find(r => r.test === 'Capabilities');
  
  if (authConfig && oauth2Config) {
    console.log(colorize('\n🔧 Current Configuration:', 'bold'));
    console.log(colorize(`OAuth2: ${oauth2Config.oauth2 || 'UNKNOWN'}`, 'blue'));
    if (authConfig.config) {
      console.log(colorize(`Connection Auth: ${authConfig.config.connectionAuthEnabled ? 'ENABLED' : 'DISABLED'}`, 'blue'));
      console.log(colorize(`Per-Request Auth: ${authConfig.config.perRequestAuthEnabled ? 'ENABLED' : 'DISABLED'}`, 'blue'));
    }
  }

  return results;
}

// Run the test
testCurrentConfiguration().catch(error => {
  console.error(colorize(`\n❌ Test failed: ${error.message}`, 'red'));
  process.exit(1);
});
