#!/usr/bin/env node

/**
 * Simple Docker Test for Enhanced Blackbox Testing
 * 
 * Basic validation of the Docker setup and server connectivity
 */

const http = require('http');
const { writeFileSync, mkdirSync, existsSync } = require('fs');
const { resolve } = require('path');

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          ok: res.statusCode >= 200 && res.statusCode < 300
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

class SimpleDockerTest {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
    this.servers = {
      oauth2Disabled: process.env.MCP_SERVER_OAUTH2_DISABLED_URL || 'http://localhost:3000',
      oauth2Enabled: process.env.MCP_SERVER_OAUTH2_ENABLED_URL || 'http://localhost:3001',
      connectionAuth: process.env.MCP_SERVER_CONNECTION_AUTH_URL || 'http://localhost:3002'
    };
  }

  async runTests() {
    console.log(colorize('\n🧪 Simple Docker Test for Enhanced Blackbox Testing', 'bold'));
    console.log(colorize('Testing basic server connectivity and health checks', 'blue'));
    console.log('='.repeat(70));

    try {
      // Test basic connectivity
      await this.testServerConnectivity();
      
      // Generate simple report
      await this.generateReport();

    } catch (error) {
      console.error(colorize(`❌ Test failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testServerConnectivity() {
    console.log(colorize('\n🔗 Testing Server Connectivity...', 'cyan'));
    
    for (const [name, url] of Object.entries(this.servers)) {
      try {
        console.log(`Testing ${name} at ${url}...`);
        
        // Test health endpoint
        const healthResponse = await makeRequest(`${url}/health`);
        
        if (healthResponse.ok) {
          console.log(colorize(`✅ ${name} health check passed`, 'green'));
          this.results.push({
            server: name,
            test: 'health_check',
            status: 'PASS',
            url: url,
            httpStatus: healthResponse.status
          });
        } else {
          console.log(colorize(`❌ ${name} health check failed (HTTP ${healthResponse.status})`, 'red'));
          this.results.push({
            server: name,
            test: 'health_check',
            status: 'FAIL',
            url: url,
            httpStatus: healthResponse.status,
            error: `HTTP ${healthResponse.status}`
          });
        }

        // Test MCP capabilities endpoint
        try {
          const capabilitiesResponse = await makeRequest(`${url}/mcp/capabilities`);
          
          if (capabilitiesResponse.ok) {
            console.log(colorize(`✅ ${name} MCP capabilities accessible`, 'green'));
            this.results.push({
              server: name,
              test: 'mcp_capabilities',
              status: 'PASS',
              url: url,
              httpStatus: capabilitiesResponse.status
            });
          } else {
            console.log(colorize(`⚠️  ${name} MCP capabilities returned HTTP ${capabilitiesResponse.status}`, 'yellow'));
            this.results.push({
              server: name,
              test: 'mcp_capabilities',
              status: 'PARTIAL',
              url: url,
              httpStatus: capabilitiesResponse.status
            });
          }
        } catch (capError) {
          console.log(colorize(`⚠️  ${name} MCP capabilities test failed: ${capError.message}`, 'yellow'));
          this.results.push({
            server: name,
            test: 'mcp_capabilities',
            status: 'FAIL',
            url: url,
            error: capError.message
          });
        }

      } catch (error) {
        console.log(colorize(`❌ ${name} connection failed: ${error.message}`, 'red'));
        this.results.push({
          server: name,
          test: 'connectivity',
          status: 'FAIL',
          url: url,
          error: error.message
        });
      }
    }
  }

  async generateReport() {
    const duration = Math.round((Date.now() - this.startTime) / 1000);
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const partialTests = this.results.filter(r => r.status === 'PARTIAL').length;
    
    const report = {
      timestamp: new Date().toISOString(),
      duration,
      summary: {
        totalTests,
        passed: passedTests,
        failed: failedTests,
        partial: partialTests,
        successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
      },
      results: this.results,
      environment: {
        nodeEnv: process.env.NODE_ENV || 'test',
        servers: this.servers
      }
    };

    // Ensure results directory exists
    const resultsDir = resolve(__dirname, '../results');
    if (!existsSync(resultsDir)) {
      mkdirSync(resultsDir, { recursive: true });
    }

    // Write report
    const reportFile = resolve(resultsDir, `simple-docker-test-report-${Date.now()}.json`);
    writeFileSync(reportFile, JSON.stringify(report, null, 2));

    // Print summary
    console.log(colorize('\n📊 Test Summary', 'bold'));
    console.log('='.repeat(40));
    console.log(`Duration: ${duration}s`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(colorize(`Passed: ${passedTests}`, 'green'));
    console.log(colorize(`Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'green'));
    console.log(colorize(`Partial: ${partialTests}`, partialTests > 0 ? 'yellow' : 'green'));
    console.log(colorize(`Success Rate: ${report.summary.successRate}%`, 
      report.summary.successRate >= 70 ? 'green' : 'red'));
    
    console.log(colorize(`\n📄 Report saved: ${reportFile}`, 'blue'));

    // Exit with appropriate code
    process.exit(failedTests === 0 ? 0 : 1);
  }
}

// Run the test
const test = new SimpleDockerTest();
test.runTests().catch(error => {
  console.error(colorize(`Fatal error: ${error.message}`, 'red'));
  process.exit(1);
});
