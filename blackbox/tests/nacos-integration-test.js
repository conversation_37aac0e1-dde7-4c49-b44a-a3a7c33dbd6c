#!/usr/bin/env node

/**
 * NACOS Integration Test
 * 
 * Tests NACOS configuration loading and integration with the MCP server.
 * This test validates that the MCP server can properly load configuration
 * from NACOS and fall back to environment variables when needed.
 */

import { spawn } from 'child_process';
import fetch from 'node-fetch';

// Colors for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class NacosIntegrationTest {
  constructor() {
    this.testResults = [];
    this.serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3000';
  }

  async runTests() {
    console.log(colorize('\n🔧 NACOS Integration Test Suite', 'cyan'));
    console.log(colorize('=====================================', 'cyan'));

    try {
      await this.testServerHealth();
      await this.testNacosConfigLoading();
      await this.testConfigurationEndpoint();
      await this.testEnvironmentFallback();
      await this.generateReport();
    } catch (error) {
      console.error(colorize(`❌ Test suite failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testServerHealth() {
    console.log(colorize('\n🏥 Testing Server Health...', 'blue'));
    
    try {
      const response = await fetch(`${this.serverUrl}/health`, {
        timeout: 5000
      });
      
      if (response.ok) {
        const health = await response.json();
        this.addResult('Server Health Check', 'PASS', {
          status: health.status,
          timestamp: health.timestamp
        });
        console.log(colorize('✅ Server is healthy', 'green'));
      } else {
        throw new Error(`Health check failed with status: ${response.status}`);
      }
    } catch (error) {
      this.addResult('Server Health Check', 'FAIL', { error: error.message });
      console.log(colorize(`❌ Server health check failed: ${error.message}`, 'red'));
      throw error;
    }
  }

  async testNacosConfigLoading() {
    console.log(colorize('\n🔧 Testing NACOS Configuration Loading...', 'blue'));
    
    try {
      // Test if server can load NACOS configuration
      const response = await fetch(`${this.serverUrl}/debug/config`, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const config = await response.json();
        
        // Check if NACOS configuration is present
        const hasNacosConfig = config.nacos && config.nacos.initialized;
        const configKeys = config.environment ? Object.keys(config.environment) : [];
        
        this.addResult('NACOS Configuration Loading', hasNacosConfig ? 'PASS' : 'WARN', {
          nacosInitialized: hasNacosConfig,
          configKeysCount: configKeys.length,
          sampleKeys: configKeys.slice(0, 5)
        });
        
        if (hasNacosConfig) {
          console.log(colorize('✅ NACOS configuration loaded successfully', 'green'));
          console.log(colorize(`📊 Configuration keys loaded: ${configKeys.length}`, 'blue'));
        } else {
          console.log(colorize('⚠️  NACOS not initialized, using environment variables', 'yellow'));
        }
      } else {
        throw new Error(`Config endpoint returned status: ${response.status}`);
      }
    } catch (error) {
      this.addResult('NACOS Configuration Loading', 'FAIL', { error: error.message });
      console.log(colorize(`❌ NACOS config test failed: ${error.message}`, 'red'));
      // Don't throw here - this might be expected in some test environments
    }
  }

  async testConfigurationEndpoint() {
    console.log(colorize('\n⚙️  Testing Configuration Endpoint...', 'blue'));
    
    try {
      const response = await fetch(`${this.serverUrl}/debug/env`, {
        timeout: 5000,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const env = await response.json();
        
        // Check for required configuration keys
        const requiredKeys = [
          'NODE_ENV',
          'TRANSPORT',
          'MCP_SERVER_PORT',
          'VITE_API_HOST'
        ];
        
        const missingKeys = requiredKeys.filter(key => !env[key]);
        const hasAllRequired = missingKeys.length === 0;
        
        this.addResult('Configuration Endpoint', hasAllRequired ? 'PASS' : 'FAIL', {
          requiredKeys,
          missingKeys,
          totalKeys: Object.keys(env).length
        });
        
        if (hasAllRequired) {
          console.log(colorize('✅ All required configuration keys present', 'green'));
        } else {
          console.log(colorize(`❌ Missing required keys: ${missingKeys.join(', ')}`, 'red'));
        }
      } else {
        throw new Error(`Environment endpoint returned status: ${response.status}`);
      }
    } catch (error) {
      this.addResult('Configuration Endpoint', 'FAIL', { error: error.message });
      console.log(colorize(`❌ Configuration endpoint test failed: ${error.message}`, 'red'));
    }
  }

  async testEnvironmentFallback() {
    console.log(colorize('\n🔄 Testing Environment Variable Fallback...', 'blue'));
    
    try {
      // Test that environment variables are properly used as fallbacks
      const response = await fetch(`${this.serverUrl}/debug/config-source`, {
        timeout: 5000,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const configSource = await response.json();
        
        const envCount = configSource.fromEnvironment || 0;
        const nacosCount = configSource.fromNacos || 0;
        const totalCount = envCount + nacosCount;
        
        this.addResult('Environment Fallback', 'PASS', {
          fromEnvironment: envCount,
          fromNacos: nacosCount,
          total: totalCount,
          fallbackWorking: envCount > 0
        });
        
        console.log(colorize(`✅ Configuration sources: ${envCount} from env, ${nacosCount} from NACOS`, 'green'));
      } else {
        // This endpoint might not exist, which is okay
        this.addResult('Environment Fallback', 'SKIP', { 
          reason: 'Config source endpoint not available' 
        });
        console.log(colorize('⏭️  Config source endpoint not available (optional)', 'yellow'));
      }
    } catch (error) {
      this.addResult('Environment Fallback', 'SKIP', { 
        reason: 'Config source endpoint not available',
        error: error.message 
      });
      console.log(colorize('⏭️  Environment fallback test skipped (optional endpoint)', 'yellow'));
    }
  }

  addResult(testName, status, details = {}) {
    this.testResults.push({
      test: testName,
      status,
      details,
      timestamp: new Date().toISOString()
    });
  }

  async generateReport() {
    console.log(colorize('\n📊 NACOS Integration Test Report', 'cyan'));
    console.log(colorize('=====================================', 'cyan'));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const warned = this.testResults.filter(r => r.status === 'WARN').length;
    const skipped = this.testResults.filter(r => r.status === 'SKIP').length;

    console.log(`\n📈 Test Summary:`);
    console.log(colorize(`  ✅ Passed: ${passed}`, 'green'));
    console.log(colorize(`  ❌ Failed: ${failed}`, failed > 0 ? 'red' : 'green'));
    console.log(colorize(`  ⚠️  Warnings: ${warned}`, warned > 0 ? 'yellow' : 'green'));
    console.log(colorize(`  ⏭️  Skipped: ${skipped}`, 'blue'));

    console.log(`\n📋 Detailed Results:`);
    this.testResults.forEach(result => {
      const icon = {
        'PASS': '✅',
        'FAIL': '❌',
        'WARN': '⚠️ ',
        'SKIP': '⏭️ '
      }[result.status];
      
      const color = {
        'PASS': 'green',
        'FAIL': 'red',
        'WARN': 'yellow',
        'SKIP': 'blue'
      }[result.status];

      console.log(colorize(`  ${icon} ${result.test}: ${result.status}`, color));
      
      if (result.details && Object.keys(result.details).length > 0) {
        console.log(`     ${JSON.stringify(result.details, null, 2).replace(/\n/g, '\n     ')}`);
      }
    });

    console.log(colorize('\n🎯 NACOS Integration Test Complete!', 'cyan'));
    
    if (failed > 0) {
      console.log(colorize('❌ Some tests failed. Check the details above.', 'red'));
      process.exit(1);
    } else {
      console.log(colorize('✅ All critical tests passed!', 'green'));
    }
  }
}

// Run the test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new NacosIntegrationTest();
  test.runTests().catch(error => {
    console.error(colorize(`❌ Test execution failed: ${error.message}`, 'red'));
    process.exit(1);
  });
}

export default NacosIntegrationTest;
