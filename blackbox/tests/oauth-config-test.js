#!/usr/bin/env node

/**
 * OAuth Server Behavior Test (Blackbox)
 *
 * Tests OAuth server behavior without external dependencies:
 * - OAuth enabled vs disabled server behavior
 * - Authentication mode consistency
 * - OAuth endpoint availability and consistency
 * - Server configuration self-consistency
 * - Authentication behavior validation
 *
 * This is a pure blackbox test that only validates server responses
 * and internal consistency, without depending on external .env files.
 *
 * Usage:
 *   node blackbox/tests/oauth-config-test.js
 */

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * OAuth Server Behavior Test Suite (Blackbox)
 * Validates OAuth server behavior through API responses only
 */
class OAuthServerTestSuite {
  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.serverOAuth2Enabled = null;
    this.serverAuthConfig = null;
  }

  async runServerTests() {
    console.log(colorize('\n⚙️  OAuth Server Behavior Tests (Blackbox)', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Test server behavior without external dependencies
      await this.testOAuth2EnabledBehavior();
      await this.testAuthenticationModes();
      await this.testServerConsistency();

      this.printResults();
      return this.results;
    } catch (error) {
      console.error(colorize(`❌ Server test failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'OAuth Server Test Suite',
        status: 'FAIL',
        error: error.message,
      });
      throw error;
    }
  }

  async testServerConsistency() {
    console.log(colorize('\n🔍 Test 4: Server Internal Consistency', 'cyan'));

    // Test that server's OAuth status is consistent with its endpoints
    if (this.serverOAuth2Enabled !== null && this.serverAuthConfig !== null) {
      console.log(colorize(`✅ Server configuration retrieved successfully`, 'green'));
      console.log(colorize(`   OAuth2 Enabled: ${this.serverOAuth2Enabled}`, 'blue'));
      console.log(
        colorize(`   Connection Auth: ${this.serverAuthConfig.connectionAuthEnabled}`, 'blue')
      );
      console.log(
        colorize(`   Per-Request Auth: ${this.serverAuthConfig.perRequestAuthEnabled}`, 'blue')
      );
      console.log(
        colorize(`   Connection Auth Strict: ${this.serverAuthConfig.connectionAuthStrict}`, 'blue')
      );

      this.results.push({
        test: 'Server Configuration Retrieval',
        status: 'PASS',
        details: {
          oauth2_enabled: this.serverOAuth2Enabled,
          auth_config: this.serverAuthConfig,
        },
      });
    } else {
      console.log(colorize(`❌ Failed to retrieve complete server configuration`, 'red'));
      this.results.push({
        test: 'Server Configuration Retrieval',
        status: 'FAIL',
        error: 'Could not retrieve server configuration',
      });
    }
  }

  async testOAuth2EnabledBehavior() {
    console.log(colorize('\n🔐 Test 1: OAuth2 Server Behavior Validation', 'cyan'));

    // Test capabilities endpoint
    const capabilitiesResponse = await this.makeRequest('/mcp/capabilities');

    if (capabilitiesResponse.status === 200) {
      const capabilities = JSON.parse(capabilitiesResponse.data);
      const reportedOAuth2 = capabilities.oauth2_enabled;

      // Blackbox test: Just validate that server reports a consistent OAuth status
      console.log(colorize(`✅ Server reports OAuth2 status: ${reportedOAuth2}`, 'green'));
      this.results.push({
        test: 'OAuth2 Status Reporting',
        status: 'PASS',
        details: {
          oauth2_enabled: reportedOAuth2,
        },
      });

      // Store the server's reported OAuth status for consistency checks
      this.serverOAuth2Enabled = reportedOAuth2;
    } else {
      console.log(colorize(`❌ Failed to get capabilities: ${capabilitiesResponse.status}`, 'red'));
      this.results.push({
        test: 'OAuth2 Status Reporting',
        status: 'FAIL',
        error: `Capabilities endpoint failed: ${capabilitiesResponse.status}`,
      });
      return;
    }

    // Test OAuth metadata endpoint consistency with reported status
    const metadataResponse = await this.makeRequest('/.well-known/oauth-protected-resource');

    if (this.serverOAuth2Enabled) {
      // If server reports OAuth enabled, metadata should be available
      if (metadataResponse.status === 200) {
        console.log(
          colorize(`✅ OAuth metadata available (consistent with OAuth enabled)`, 'green')
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'PASS',
          details: { oauth_enabled: true, metadata_available: true },
        });
      } else {
        console.log(
          colorize(
            `❌ OAuth metadata unavailable despite OAuth being enabled: ${metadataResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'FAIL',
          error: `OAuth enabled but metadata endpoint returned ${metadataResponse.status}`,
        });
      }
    } else {
      // If server reports OAuth disabled, metadata should not be available
      if (metadataResponse.status === 404) {
        console.log(
          colorize(`✅ OAuth metadata unavailable (consistent with OAuth disabled)`, 'green')
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'PASS',
          details: { oauth_enabled: false, metadata_available: false },
        });
      } else {
        console.log(
          colorize(
            `❌ OAuth metadata available despite OAuth being disabled: ${metadataResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'FAIL',
          error: `OAuth disabled but metadata endpoint returned ${metadataResponse.status}`,
        });
      }
    }
  }

  async testAuthenticationModes() {
    console.log(colorize('\n🔑 Test 2: Authentication Mode Consistency', 'cyan'));

    // Test auth status endpoint
    const authStatusResponse = await this.makeRequest('/auth/status');

    if (authStatusResponse.status === 200) {
      const authStatus = JSON.parse(authStatusResponse.data);
      const config = authStatus.config;

      // Store server's actual auth configuration for behavior testing
      this.serverAuthConfig = {
        connectionAuthEnabled: config.connectionAuthEnabled,
        perRequestAuthEnabled: config.perRequestAuthEnabled,
        connectionAuthStrict: config.connectionAuthStrict,
      };

      // Blackbox test: Validate internal consistency of auth modes
      console.log(colorize(`✅ Connection Auth Mode: ${config.connectionAuthEnabled}`, 'green'));
      this.results.push({
        test: 'Connection Auth Mode Reporting',
        status: 'PASS',
        details: { connectionAuthEnabled: config.connectionAuthEnabled },
      });

      console.log(colorize(`✅ Per-Request Auth Mode: ${config.perRequestAuthEnabled}`, 'green'));
      this.results.push({
        test: 'Per-Request Auth Mode Reporting',
        status: 'PASS',
        details: { perRequestAuthEnabled: config.perRequestAuthEnabled },
      });

      console.log(colorize(`✅ Connection Auth Strict: ${config.connectionAuthStrict}`, 'green'));
      this.results.push({
        test: 'Connection Auth Strict Reporting',
        status: 'PASS',
        details: { connectionAuthStrict: config.connectionAuthStrict },
      });

      // Validate logical consistency: if OAuth is disabled, auth modes should be disabled
      if (!this.serverOAuth2Enabled) {
        const authModesDisabled = !config.connectionAuthEnabled && !config.perRequestAuthEnabled;
        if (authModesDisabled) {
          console.log(colorize(`✅ Auth modes correctly disabled when OAuth is disabled`, 'green'));
          this.results.push({
            test: 'OAuth-Auth Mode Consistency',
            status: 'PASS',
            details: { oauth_disabled: true, auth_modes_disabled: true },
          });
        } else {
          console.log(colorize(`⚠️  Auth modes enabled despite OAuth being disabled`, 'yellow'));
          this.results.push({
            test: 'OAuth-Auth Mode Consistency',
            status: 'PARTIAL',
            note: 'Auth modes enabled despite OAuth being disabled',
          });
        }
      } else {
        console.log(colorize(`✅ OAuth enabled - auth modes configuration accepted`, 'green'));
        this.results.push({
          test: 'OAuth-Auth Mode Consistency',
          status: 'PASS',
          details: { oauth_enabled: true, auth_modes_configured: true },
        });
      }
    } else {
      console.log(colorize(`❌ Auth status endpoint failed: ${authStatusResponse.status}`, 'red'));
      this.results.push({
        test: 'Authentication Mode Reporting',
        status: 'FAIL',
        error: `Auth status endpoint failed: ${authStatusResponse.status}`,
      });
    }

    // Test actual authentication behavior
    await this.testAuthenticationBehavior();
  }

  async testAuthenticationBehavior() {
    console.log(colorize('\n🔒 Test 3: Authentication Behavior Consistency', 'cyan'));

    // Test MCP request without authentication
    const noAuthResponse = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'config-test', version: '1.0.0' },
        },
      },
    });

    // Use server's actual reported configuration, not external .env
    const serverAuthRequired =
      this.serverAuthConfig &&
      (this.serverAuthConfig.connectionAuthEnabled || this.serverAuthConfig.perRequestAuthEnabled);

    if (serverAuthRequired) {
      // Server reports auth is required - should return 401 without auth
      if (noAuthResponse.status === 401) {
        console.log(
          colorize(
            `✅ Authentication correctly required (consistent with server config): 401`,
            'green'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PASS',
          details: {
            server_requires_auth: true,
            response_status: 401,
            consistent: true,
          },
        });
      } else {
        console.log(
          colorize(
            `❌ Authentication not enforced despite server config: ${noAuthResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'FAIL',
          error: `Server config requires auth but got ${noAuthResponse.status} instead of 401`,
        });
      }
    } else {
      // Server reports auth is not required - should allow access
      if (noAuthResponse.status === 200 || noAuthResponse.status === 406) {
        console.log(
          colorize(
            `✅ No authentication required (consistent with server config): ${noAuthResponse.status}`,
            'green'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PASS',
          details: {
            server_requires_auth: false,
            response_status: noAuthResponse.status,
            consistent: true,
          },
        });
      } else if (noAuthResponse.status === 401) {
        console.log(
          colorize(
            `❌ Authentication required despite server config indicating otherwise: 401`,
            'red'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'FAIL',
          error: 'Server config suggests no auth required but got 401',
        });
      } else {
        console.log(colorize(`⚠️  Unexpected response status: ${noAuthResponse.status}`, 'yellow'));
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PARTIAL',
          note: `Unexpected status: ${noAuthResponse.status}`,
        });
      }
    }
  }

  async testProviderConfiguration() {
    console.log(colorize('\n🏢 Test 4: Provider Configuration', 'cyan'));

    if (!this.config.oauth2Enabled) {
      console.log(colorize('⏭️  Skipping provider tests - OAuth2 disabled', 'yellow'));
      this.results.push({
        test: 'Provider Configuration',
        status: 'SKIP',
        note: 'OAuth2 disabled',
      });
      return;
    }

    const requiredProviderVars = ['KEYCLOAK_BASE_URL', 'KEYCLOAK_REALM'];

    const missingVars = requiredProviderVars.filter(varName => !process.env[varName]);

    if (missingVars.length === 0) {
      console.log(colorize(`✅ All provider variables configured`, 'green'));

      // Test if provider URLs are accessible
      const keycloakUrl = `${this.config.keycloakBaseUrl}/realms/${this.config.keycloakRealm}`;

      try {
        const providerResponse = await this.makeRequest('', { baseUrl: keycloakUrl });
        if (providerResponse.status === 200) {
          console.log(colorize(`✅ Provider accessible: ${keycloakUrl}`, 'green'));
          this.results.push({
            test: 'Provider Accessibility',
            status: 'PASS',
            details: { provider_url: keycloakUrl },
          });
        } else {
          console.log(
            colorize(`⚠️  Provider not accessible: ${providerResponse.status}`, 'yellow')
          );
          this.results.push({
            test: 'Provider Accessibility',
            status: 'PARTIAL',
            note: `Provider returned ${providerResponse.status}`,
          });
        }
      } catch (error) {
        console.log(colorize(`⚠️  Provider connection failed: ${error.message}`, 'yellow'));
        this.results.push({
          test: 'Provider Accessibility',
          status: 'PARTIAL',
          note: `Connection failed: ${error.message}`,
        });
      }

      this.results.push({
        test: 'Provider Configuration',
        status: 'PASS',
        details: {
          keycloak_url: this.config.keycloakBaseUrl,
          realm: this.config.keycloakRealm,
        },
      });
    } else {
      console.log(colorize(`❌ Missing provider variables: ${missingVars.join(', ')}`, 'red'));
      this.results.push({
        test: 'Provider Configuration',
        status: 'FAIL',
        error: `Missing variables: ${missingVars.join(', ')}`,
      });
    }
  }

  async testClientConfiguration() {
    console.log(colorize('\n👤 Test 5: Client Configuration', 'cyan'));

    if (!this.config.oauth2Enabled) {
      console.log(colorize('⏭️  Skipping client tests - OAuth2 disabled', 'yellow'));
      this.results.push({
        test: 'Client Configuration',
        status: 'SKIP',
        note: 'OAuth2 disabled',
      });
      return;
    }

    const clientTests = [
      {
        name: 'Client ID configured',
        condition: !!this.config.clientId,
        value: this.config.clientId,
        required: true,
      },
      {
        name: 'Client Secret configured',
        condition: !!this.config.clientSecret,
        value: this.config.clientSecret ? '[SET]' : null,
        required: this.config.clientAuthMethod !== 'none', // Only required if not using 'none' auth method
      },
      {
        name: 'Client Auth Method configured',
        condition: !!this.config.clientAuthMethod,
        value: this.config.clientAuthMethod,
        required: true,
      },
      {
        name: 'Redirect URI configured',
        condition: !!this.config.redirectUri,
        value: this.config.redirectUri,
        required: false, // Optional for some OAuth flows
      },
    ];

    clientTests.forEach(test => {
      if (test.condition) {
        console.log(colorize(`✅ ${test.name}: ${test.value}`, 'green'));
        this.results.push({
          test: test.name,
          status: 'PASS',
          details: { configured: true },
        });
      } else if (test.required) {
        console.log(colorize(`❌ ${test.name}: Required but not configured`, 'red'));
        this.results.push({
          test: test.name,
          status: 'FAIL',
          error: 'Required configuration missing',
        });
      } else {
        console.log(colorize(`⚠️  ${test.name}: Optional, not configured`, 'yellow'));
        this.results.push({
          test: test.name,
          status: 'PASS',
          note: 'Optional configuration not set',
        });
      }
    });
  }

  async testEnvironmentValidation() {
    console.log(colorize('\n🌍 Test 6: Environment Validation', 'cyan'));

    const allEnvVars = {
      OAUTH2_ENABLED: process.env.OAUTH2_ENABLED,
      KEYCLOAK_BASE_URL: process.env.KEYCLOAK_BASE_URL,
      KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
      OAUTH2_CLIENT_ID: process.env.OAUTH2_CLIENT_ID,
      OAUTH2_CLIENT_SECRET: process.env.OAUTH2_CLIENT_SECRET ? '[SET]' : undefined,
      OAUTH2_CLIENT_AUTH_METHOD: process.env.OAUTH2_CLIENT_AUTH_METHOD,
      OAUTH2_REDIRECT_URI: process.env.OAUTH2_REDIRECT_URI,
      OAUTH2_SCOPES: process.env.OAUTH2_SCOPES,
      MCP_SERVER_PUBLIC_BASE_URL: process.env.MCP_SERVER_PUBLIC_BASE_URL,
      CONNECTION_AUTH_ENABLED: process.env.CONNECTION_AUTH_ENABLED,
      PER_REQUEST_AUTH_ENABLED: process.env.PER_REQUEST_AUTH_ENABLED,
      CONNECTION_AUTH_STRICT: process.env.CONNECTION_AUTH_STRICT,
    };

    console.log(colorize('   Environment Variables:', 'blue'));
    Object.entries(allEnvVars).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(colorize(`   ${status} ${key}: ${value || 'Not set'}`, value ? 'green' : 'red'));
    });

    // Validate critical combinations
    const validationTests = [
      {
        name: 'OAuth2 enabled requires Keycloak config',
        condition:
          !this.config.oauth2Enabled || (this.config.keycloakBaseUrl && this.config.keycloakRealm),
        error: 'OAuth2 enabled but Keycloak not configured',
      },
      {
        name: 'Client credentials consistency',
        condition:
          !this.config.oauth2Enabled ||
          this.config.clientAuthMethod === 'none' ||
          this.config.clientSecret,
        error: 'Client secret auth method requires client secret',
      },
      {
        name: 'Server base URL configured',
        condition: !!this.config.serverBaseUrl,
        error: 'MCP_SERVER_PUBLIC_BASE_URL not configured',
        required: false, // Make this optional for now
      },
    ];

    validationTests.forEach(test => {
      if (test.condition) {
        console.log(colorize(`✅ ${test.name}`, 'green'));
        this.results.push({
          test: test.name,
          status: 'PASS',
        });
      } else if (test.required === false) {
        console.log(colorize(`⚠️  ${test.name}: ${test.error} (optional)`, 'yellow'));
        this.results.push({
          test: test.name,
          status: 'PASS',
          note: test.error + ' (optional)',
        });
      } else {
        console.log(colorize(`❌ ${test.name}: ${test.error}`, 'red'));
        this.results.push({
          test: test.name,
          status: 'FAIL',
          error: test.error,
        });
      }
    });
  }

  async makeRequest(path, options = {}) {
    const baseUrl = options.baseUrl || this.serverUrl;
    const url = `${baseUrl}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'User-Agent': 'oauth-config-test/1.0.0',
          ...headers,
        },
        body: typeof body === 'string' ? body : body ? JSON.stringify(body) : undefined,
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text,
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  printResults() {
    console.log(colorize('\n📊 OAuth Configuration Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (partial > 0) console.log(colorize(`Partial: ${partial}`, 'yellow'));
    if (skipped > 0) console.log(colorize(`Skipped: ${skipped}`, 'yellow'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    const successRate = Math.round(((passed + partial + skipped) / total) * 100);
    console.log(
      colorize(
        `\n🎯 Success Rate: ${successRate}%`,
        successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'
      )
    );

    // Configuration summary
    console.log(colorize('\n⚙️  Configuration Summary:', 'bold'));
    console.log(
      colorize(
        `OAuth2: ${this.config.oauth2Enabled ? 'ENABLED' : 'DISABLED'}`,
        this.config.oauth2Enabled ? 'green' : 'red'
      )
    );

    if (this.config.oauth2Enabled) {
      console.log(colorize(`Provider: Keycloak (${this.config.keycloakBaseUrl})`, 'blue'));
      console.log(colorize(`Realm: ${this.config.keycloakRealm}`, 'blue'));
      console.log(colorize(`Client: ${this.config.clientId}`, 'blue'));
      console.log(colorize(`Auth Method: ${this.config.clientAuthMethod}`, 'blue'));
    }

    console.log(
      colorize(
        `Connection Auth: ${this.config.connectionAuthEnabled ? 'ENABLED' : 'DISABLED'}`,
        this.config.connectionAuthEnabled ? 'green' : 'red'
      )
    );
    console.log(
      colorize(
        `Per-Request Auth: ${this.config.perRequestAuthEnabled ? 'ENABLED' : 'DISABLED'}`,
        this.config.perRequestAuthEnabled ? 'green' : 'red'
      )
    );

    // Detailed results
    console.log(colorize('\n📋 Detailed Results:', 'bold'));
    this.results.forEach(result => {
      const status =
        result.status === 'PASS'
          ? '✅'
          : result.status === 'PARTIAL'
            ? '⚠️'
            : result.status === 'SKIP'
              ? '⏭️'
              : '❌';
      console.log(`${status} ${result.test}`);
      if (result.note) {
        console.log(colorize(`    Note: ${result.note}`, 'yellow'));
      }
      if (result.error) {
        console.log(colorize(`    Error: ${result.error}`, 'red'));
      }
    });

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        configuration: this.config,
        summary: { total, passed, partial, skipped, failed, successRate },
        details: this.results,
      };

      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run the test
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const testSuite = new OAuthServerTestSuite(serverUrl);
testSuite.runServerTests().catch(console.error);
