#!/usr/bin/env node

/**
 * MCP Client Simulator
 * 
 * Simulates a real MCP client interacting with the server
 * Tests the complete MCP protocol flow
 * 
 * Usage:
 *   node test/mcp-client-simulator.js
 *   npm run test:mcp-client
 */

// Use native fetch instead of EventSource for simplicity
import { performance } from 'perf_hooks';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class MCPClientSimulator {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.sessionId = null;
    this.eventSource = null;
    this.results = [];
    this.requestId = 1;
  }

  async runSimulation() {
    console.log(colorize('\n🤖 MCP Client Simulator', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Step 1: Initialize MCP Connection
      await this.testMCPInitialize();
      
      // Step 2: List Available Tools
      await this.testListTools();
      
      // Step 3: Test Tool Execution
      await this.testToolExecution();
      
      // Step 4: Test Error Handling
      await this.testErrorHandling();
      
      // Step 5: Test Session Management
      await this.testSessionManagement();

      // Summary
      this.printResults();

    } catch (error) {
      console.error(colorize(`❌ Simulation failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testMCPInitialize() {
    console.log(colorize('\n📡 Step 1: MCP Initialize', 'cyan'));
    
    const startTime = performance.now();
    const response = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            roots: {
              listChanged: true
            },
            sampling: {}
          },
          clientInfo: {
            name: 'mcp-client-simulator',
            version: '1.0.0'
          }
        }
      }
    });

    const duration = performance.now() - startTime;

    if (response.status === 200) {
      // Parse Server-Sent Events response
      const lines = response.data.split('\n');
      let jsonData = null;
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            jsonData = JSON.parse(line.substring(6));
            break;
          } catch (e) {
            // Continue parsing
          }
        }
      }

      if (jsonData && jsonData.result) {
        // Extract session ID from response headers
        if (response.headers['mcp-session-id']) {
          this.sessionId = response.headers['mcp-session-id'];
          console.log(colorize(`   Session ID: ${this.sessionId}`, 'blue'));
        }

        console.log(colorize(`✅ Initialize successful (${Math.round(duration)}ms)`, 'green'));
        console.log(colorize(`   Protocol: ${jsonData.result.protocolVersion}`, 'blue'));
        console.log(colorize(`   Server: ${jsonData.result.serverInfo.name} v${jsonData.result.serverInfo.version}`, 'blue'));

        this.results.push({
          test: 'MCP Initialize',
          status: 'PASS',
          duration: Math.round(duration),
          details: jsonData.result
        });
      } else {
        throw new Error('Invalid initialize response format');
      }
    } else {
      throw new Error(`Initialize failed with status ${response.status}`);
    }
  }

  async testListTools() {
    console.log(colorize('\n🔧 Step 2: List Tools', 'cyan'));
    
    const startTime = performance.now();
    const response = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'tools/list',
        params: {}
      }
    });

    const duration = performance.now() - startTime;

    if (response.status === 200) {
      const lines = response.data.split('\n');
      let jsonData = null;
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            jsonData = JSON.parse(line.substring(6));
            break;
          } catch (e) {
            // Continue parsing
          }
        }
      }

      if (jsonData && jsonData.result && jsonData.result.tools) {
        const toolCount = jsonData.result.tools.length;
        console.log(colorize(`✅ Tools listed successfully (${Math.round(duration)}ms)`, 'green'));
        console.log(colorize(`   Found ${toolCount} tools`, 'blue'));
        
        // Show first few tools
        jsonData.result.tools.slice(0, 3).forEach(tool => {
          console.log(colorize(`   - ${tool.name}: ${tool.description}`, 'blue'));
        });
        
        if (toolCount > 3) {
          console.log(colorize(`   ... and ${toolCount - 3} more tools`, 'blue'));
        }

        this.results.push({
          test: 'List Tools',
          status: 'PASS',
          duration: Math.round(duration),
          details: { toolCount }
        });
      } else {
        throw new Error('Invalid tools/list response format');
      }
    } else {
      throw new Error(`List tools failed with status ${response.status}`);
    }
  }

  async testToolExecution() {
    console.log(colorize('\n⚡ Step 3: Tool Execution', 'cyan'));
    
    // Test ping tool (should be fast and reliable)
    const startTime = performance.now();
    const response = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'tools/call',
        params: {
          name: 'test_ping',
          arguments: {}
        }
      }
    });

    const duration = performance.now() - startTime;

    if (response.status === 200) {
      const lines = response.data.split('\n');
      let jsonData = null;
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            jsonData = JSON.parse(line.substring(6));
            break;
          } catch (e) {
            // Continue parsing
          }
        }
      }

      if (jsonData && jsonData.result) {
        console.log(colorize(`✅ Tool execution successful (${Math.round(duration)}ms)`, 'green'));
        console.log(colorize(`   Tool: test_ping`, 'blue'));
        console.log(colorize(`   Result: ${JSON.stringify(jsonData.result).substring(0, 100)}...`, 'blue'));

        this.results.push({
          test: 'Tool Execution',
          status: 'PASS',
          duration: Math.round(duration),
          details: { tool: 'test_ping' }
        });
      } else {
        throw new Error('Invalid tool execution response format');
      }
    } else {
      throw new Error(`Tool execution failed with status ${response.status}`);
    }
  }

  async testErrorHandling() {
    console.log(colorize('\n🚨 Step 4: Error Handling', 'cyan'));
    
    // Test invalid tool call
    const startTime = performance.now();
    const response = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'tools/call',
        params: {
          name: 'nonexistent_tool',
          arguments: {}
        }
      }
    });

    const duration = performance.now() - startTime;

    if (response.status === 200) {
      const lines = response.data.split('\n');
      let jsonData = null;
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            jsonData = JSON.parse(line.substring(6));
            break;
          } catch (e) {
            // Continue parsing
          }
        }
      }

      if (jsonData && jsonData.error) {
        console.log(colorize(`✅ Error handling working (${Math.round(duration)}ms)`, 'green'));
        console.log(colorize(`   Error code: ${jsonData.error.code}`, 'blue'));
        console.log(colorize(`   Error message: ${jsonData.error.message}`, 'blue'));

        this.results.push({
          test: 'Error Handling',
          status: 'PASS',
          duration: Math.round(duration),
          details: { errorCode: jsonData.error.code }
        });
      } else {
        console.log(colorize(`⚠️ Expected error response, got success`, 'yellow'));
        this.results.push({
          test: 'Error Handling',
          status: 'PARTIAL',
          duration: Math.round(duration),
          details: { note: 'No error returned for invalid tool' }
        });
      }
    } else {
      throw new Error(`Error handling test failed with status ${response.status}`);
    }
  }

  async testSessionManagement() {
    console.log(colorize('\n🔗 Step 5: Session Management', 'cyan'));
    
    // Test capabilities endpoint
    const startTime = performance.now();
    const response = await this.makeRequest('/mcp/capabilities', {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    const duration = performance.now() - startTime;

    if (response.status === 200) {
      const data = JSON.parse(response.data);
      console.log(colorize(`✅ Session management working (${Math.round(duration)}ms)`, 'green'));
      console.log(colorize(`   Transport: ${data.transport}`, 'blue'));
      console.log(colorize(`   Streaming: ${data.streaming}`, 'blue'));
      console.log(colorize(`   OAuth2: ${data.oauth2_enabled}`, 'blue'));

      this.results.push({
        test: 'Session Management',
        status: 'PASS',
        duration: Math.round(duration),
        details: data
      });
    } else {
      throw new Error(`Session management test failed with status ${response.status}`);
    }
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    // Add session ID to headers if we have one
    if (this.sessionId) {
      headers['mcp-session-id'] = this.sessionId;
    }

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'User-Agent': 'mcp-client-simulator/1.0.0',
          ...headers
        },
        body: body ? JSON.stringify(body) : undefined
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  printResults() {
    console.log(colorize('\n📊 MCP Client Simulation Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (partial > 0) console.log(colorize(`Partial: ${partial}`, 'yellow'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    const avgDuration = Math.round(
      this.results.reduce((sum, r) => sum + r.duration, 0) / total
    );
    console.log(colorize(`Average Duration: ${avgDuration}ms`, 'blue'));

    console.log(colorize('\n📋 Detailed Results:', 'bold'));
    this.results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : 
                    result.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`${status} ${result.test}: ${result.duration}ms`);
    });

    const successRate = Math.round((passed / total) * 100);
    console.log(colorize(`\n🎯 Success Rate: ${successRate}%`, 
      successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'));

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        summary: { total, passed, partial, failed, successRate, avgDuration },
        details: this.results
      };
      
      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run simulation
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const simulator = new MCPClientSimulator(serverUrl);
simulator.runSimulation().catch(console.error);
