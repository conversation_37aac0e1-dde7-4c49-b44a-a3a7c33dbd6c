#!/usr/bin/env node

/**
 * OAuth Endpoints Validation Test
 *
 * Comprehensive testing of all OAuth 2.0 endpoints:
 * - /.well-known/oauth-protected-resource (metadata)
 * - /register (client registration)
 * - /authorize (authorization endpoint)
 * - /token (token endpoint)
 * - /revoke (token revocation)
 *
 * Tests parameter validation, error responses, and compliance with OAuth 2.0 spec
 *
 * Usage:
 *   node blackbox/tests/oauth-endpoints-test.js
 */

import crypto from 'crypto';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * OAuth Endpoints Test Suite
 * Validates all OAuth endpoints for proper parameter handling and responses
 */
class OAuthEndpointsTestSuite {
  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.testClientId = null;
    this.testClientSecret = null;
  }

  async runEndpointsTests() {
    console.log(colorize('\n🔗 OAuth Endpoints Validation Tests', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Test all OAuth endpoints
      await this.testMetadataEndpoint();
      await this.testClientRegistrationEndpoint();
      await this.testAuthorizationEndpoint();
      await this.testTokenEndpoint();
      await this.testRevocationEndpoint();
      await this.testEndpointSecurity();

      this.printResults();
      return this.results;
    } catch (error) {
      console.error(colorize(`❌ Endpoints test failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'OAuth Endpoints Test Suite',
        status: 'FAIL',
        error: error.message,
      });
      throw error;
    }
  }

  async testMetadataEndpoint() {
    console.log(colorize('\n📋 Test 1: OAuth Metadata Endpoint', 'cyan'));

    const tests = [
      {
        name: 'GET /.well-known/oauth-protected-resource',
        path: '/.well-known/oauth-protected-resource',
        method: 'GET',
        expectedStatus: [200, 404], // 404 if OAuth disabled
        validateResponse: (data, status) => {
          if (status === 200) {
            try {
              const metadata = JSON.parse(data);
              return metadata.resource && metadata.authorization_servers;
            } catch {
              return false;
            }
          }
          return true; // 404 is acceptable
        },
      },
      {
        name: 'OPTIONS /.well-known/oauth-protected-resource',
        path: '/.well-known/oauth-protected-resource',
        method: 'OPTIONS',
        expectedStatus: [200, 204, 404],
        validateResponse: () => true,
      },
    ];

    for (const test of tests) {
      await this.runEndpointTest(test);
    }
  }

  async testClientRegistrationEndpoint() {
    console.log(colorize('\n📝 Test 2: Client Registration Endpoint', 'cyan'));

    const validClientMetadata = {
      client_name: 'Test OAuth Client',
      redirect_uris: ['http://localhost:8090/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_post',
      scope: 'mcp:tools',
    };

    const tests = [
      {
        name: 'POST /register with valid metadata',
        path: '/register',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: validClientMetadata,
        expectedStatus: [200, 201],
        validateResponse: (data, status) => {
          if (status === 200 || status === 201) {
            try {
              const client = JSON.parse(data);
              this.testClientId = client.client_id;
              this.testClientSecret = client.client_secret;
              return client.client_id && client.redirect_uris;
            } catch {
              return false;
            }
          }
          return false;
        },
      },
      {
        name: 'POST /register with missing redirect_uris',
        path: '/register',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: { ...validClientMetadata, redirect_uris: undefined },
        expectedStatus: [400],
        validateResponse: data =>
          data.includes('redirect_uris') || data.includes('invalid_request'),
      },
      {
        name: 'POST /register with invalid JSON',
        path: '/register',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid-json',
        expectedStatus: [400],
        validateResponse: () => true,
      },
      {
        name: 'GET /register (should not be allowed)',
        path: '/register',
        method: 'GET',
        expectedStatus: [405, 404],
        validateResponse: () => true,
      },
    ];

    for (const test of tests) {
      await this.runEndpointTest(test);
    }
  }

  async testAuthorizationEndpoint() {
    console.log(colorize('\n🔑 Test 3: Authorization Endpoint', 'cyan'));

    if (!this.testClientId) {
      console.log(
        colorize('⚠️  Skipping authorization tests - no test client available', 'yellow')
      );
      return;
    }

    const validParams = {
      response_type: 'code',
      client_id: this.testClientId,
      redirect_uri: 'http://localhost:8090/callback',
      scope: 'mcp:tools',
      state: 'test-state-123',
      code_challenge: crypto.createHash('sha256').update('test-verifier').digest('base64url'),
      code_challenge_method: 'S256',
    };

    const tests = [
      {
        name: 'GET /authorize with valid parameters',
        path: `/authorize?${new URLSearchParams(validParams).toString()}`,
        method: 'GET',
        expectedStatus: [200, 302], // 200 for form, 302 for redirect
        validateResponse: () => true,
      },
      {
        name: 'POST /authorize with valid parameters',
        path: '/authorize',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(validParams).toString(),
        expectedStatus: [200, 302],
        validateResponse: () => true,
      },
      {
        name: 'GET /authorize missing client_id',
        path: `/authorize?${new URLSearchParams({ ...validParams, client_id: undefined }).toString()}`,
        method: 'GET',
        expectedStatus: [400],
        validateResponse: data => data.includes('client_id') || data.includes('invalid_request'),
      },
      {
        name: 'GET /authorize invalid response_type',
        path: `/authorize?${new URLSearchParams({ ...validParams, response_type: 'token' }).toString()}`,
        method: 'GET',
        expectedStatus: [302, 400],
        validateResponse: (data, status, headers) => {
          if (status === 302 && headers.location) {
            return headers.location.includes('error=unsupported_response_type');
          }
          return data.includes('unsupported_response_type');
        },
      },
      {
        name: 'GET /authorize missing PKCE challenge',
        path: `/authorize?${new URLSearchParams({ ...validParams, code_challenge: undefined }).toString()}`,
        method: 'GET',
        expectedStatus: [302, 400],
        validateResponse: (data, status, headers) => {
          if (status === 302 && headers.location) {
            return headers.location.includes('error=invalid_request');
          }
          return data.includes('invalid_request');
        },
      },
    ];

    for (const test of tests) {
      await this.runEndpointTest(test);
    }
  }

  async testTokenEndpoint() {
    console.log(colorize('\n🎫 Test 4: Token Endpoint', 'cyan'));

    if (!this.testClientId) {
      console.log(colorize('⚠️  Skipping token tests - no test client available', 'yellow'));
      return;
    }

    const validTokenParams = {
      grant_type: 'authorization_code',
      code: 'test-auth-code',
      redirect_uri: 'http://localhost:8090/callback',
      client_id: this.testClientId,
      code_verifier: 'test-verifier',
    };

    if (this.testClientSecret) {
      validTokenParams.client_secret = this.testClientSecret;
    }

    const tests = [
      {
        name: 'POST /token with authorization_code grant',
        path: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(validTokenParams).toString(),
        expectedStatus: [200, 400], // 400 for invalid code is acceptable
        validateResponse: (data, status) => {
          if (status === 200) {
            try {
              const tokens = JSON.parse(data);
              return tokens.access_token && tokens.token_type;
            } catch {
              return false;
            }
          }
          return data.includes('invalid_grant') || data.includes('invalid_request');
        },
      },
      {
        name: 'POST /token missing grant_type',
        path: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({ ...validTokenParams, grant_type: undefined }).toString(),
        expectedStatus: [400],
        validateResponse: data => data.includes('invalid_request') || data.includes('grant_type'),
      },
      {
        name: 'POST /token unsupported grant_type',
        path: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({ ...validTokenParams, grant_type: 'password' }).toString(),
        expectedStatus: [400],
        validateResponse: data => data.includes('unsupported_grant_type'),
      },
      {
        name: 'GET /token (should not be allowed)',
        path: '/token',
        method: 'GET',
        expectedStatus: [405],
        validateResponse: data => data.includes('method_not_allowed') || data.includes('Method'),
      },
      {
        name: 'POST /token with refresh_token grant',
        path: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: 'test-refresh-token',
          client_id: this.testClientId,
          ...(this.testClientSecret && { client_secret: this.testClientSecret }),
        }).toString(),
        expectedStatus: [200, 400], // 400 for invalid token is acceptable
        validateResponse: () => true,
      },
    ];

    for (const test of tests) {
      await this.runEndpointTest(test);
    }
  }

  async testRevocationEndpoint() {
    console.log(colorize('\n🚫 Test 5: Token Revocation Endpoint', 'cyan'));

    if (!this.testClientId) {
      console.log(colorize('⚠️  Skipping revocation tests - no test client available', 'yellow'));
      return;
    }

    const tests = [
      {
        name: 'POST /revoke with access token',
        path: '/revoke',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          token: 'test-access-token',
          client_id: this.testClientId,
          ...(this.testClientSecret && { client_secret: this.testClientSecret }),
        }).toString(),
        expectedStatus: [200, 204, 404], // 404 if endpoint not implemented
        validateResponse: () => true,
      },
      {
        name: 'POST /revoke missing token',
        path: '/revoke',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: this.testClientId,
          ...(this.testClientSecret && { client_secret: this.testClientSecret }),
        }).toString(),
        expectedStatus: [400, 404],
        validateResponse: (data, status) => {
          if (status === 404) return true; // Endpoint not implemented
          return data.includes('invalid_request') || data.includes('token');
        },
      },
      {
        name: 'GET /revoke (should not be allowed)',
        path: '/revoke',
        method: 'GET',
        expectedStatus: [405, 404],
        validateResponse: () => true,
      },
    ];

    for (const test of tests) {
      await this.runEndpointTest(test);
    }
  }

  async testEndpointSecurity() {
    console.log(colorize('\n🔒 Test 6: Endpoint Security', 'cyan'));

    const tests = [
      {
        name: 'CORS headers on token endpoint',
        path: '/token',
        method: 'OPTIONS',
        headers: { Origin: 'https://example.com' },
        expectedStatus: [200, 204],
        validateResponse: (data, status, headers) => {
          return headers['access-control-allow-origin'] || headers['access-control-allow-methods'];
        },
      },
      {
        name: 'Rate limiting on authorization endpoint',
        path: '/authorize?client_id=test&response_type=code',
        method: 'GET',
        expectedStatus: [200, 302, 400, 429], // 429 if rate limited
        validateResponse: () => true,
        repeat: 5, // Test multiple requests
      },
      {
        name: 'Content-Type validation on token endpoint',
        path: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }, // Wrong content type
        body: JSON.stringify({
          grant_type: 'authorization_code',
          code: 'test',
        }),
        expectedStatus: [400, 415], // Should reject wrong content type
        validateResponse: () => true,
      },
    ];

    for (const test of tests) {
      if (test.repeat) {
        // Test multiple times for rate limiting
        for (let i = 0; i < test.repeat; i++) {
          await this.runEndpointTest({
            ...test,
            name: `${test.name} (attempt ${i + 1})`,
          });
          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } else {
        await this.runEndpointTest(test);
      }
    }
  }

  async runEndpointTest(test) {
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(test.path, {
        method: test.method,
        headers: test.headers || {},
        body: test.body,
      });

      const duration = Date.now() - startTime;
      const expectedStatuses = Array.isArray(test.expectedStatus)
        ? test.expectedStatus
        : [test.expectedStatus];

      const statusMatch = expectedStatuses.includes(response.status);
      const responseValid = test.validateResponse
        ? test.validateResponse(response.data, response.status, response.headers)
        : true;

      if (statusMatch && responseValid) {
        console.log(colorize(`✅ ${test.name}: ${response.status} (${duration}ms)`, 'green'));
        this.results.push({
          test: test.name,
          status: 'PASS',
          duration,
          details: {
            status_code: response.status,
            expected_statuses: expectedStatuses,
          },
        });
      } else {
        const issue = !statusMatch
          ? `Wrong status ${response.status}, expected ${expectedStatuses.join(' or ')}`
          : 'Response validation failed';

        console.log(colorize(`❌ ${test.name}: ${issue}`, 'red'));
        this.results.push({
          test: test.name,
          status: 'FAIL',
          duration,
          error: issue,
          details: {
            actual_status: response.status,
            expected_statuses: expectedStatuses,
          },
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(colorize(`❌ ${test.name}: Request failed - ${error.message}`, 'red'));
      this.results.push({
        test: test.name,
        status: 'FAIL',
        duration,
        error: error.message,
      });
    }
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'User-Agent': 'oauth-endpoints-test/1.0.0',
          ...headers,
        },
        body: typeof body === 'string' ? body : body ? JSON.stringify(body) : undefined,
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text,
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  printResults() {
    console.log(colorize('\n📊 OAuth Endpoints Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    const successRate = Math.round((passed / total) * 100);
    console.log(
      colorize(
        `\n🎯 Success Rate: ${successRate}%`,
        successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'
      )
    );

    // Group results by endpoint
    const endpointGroups = {};
    this.results.forEach(result => {
      const endpoint = result.test.split(' ')[0] + ' ' + result.test.split(' ')[1];
      if (!endpointGroups[endpoint]) {
        endpointGroups[endpoint] = [];
      }
      endpointGroups[endpoint].push(result);
    });

    console.log(colorize('\n📋 Results by Endpoint:', 'bold'));
    Object.entries(endpointGroups).forEach(([endpoint, results]) => {
      const endpointPassed = results.filter(r => r.status === 'PASS').length;
      const endpointTotal = results.length;
      const endpointRate = Math.round((endpointPassed / endpointTotal) * 100);

      console.log(
        colorize(`\n${endpoint}: ${endpointPassed}/${endpointTotal} (${endpointRate}%)`, 'cyan')
      );
      results.forEach(result => {
        const status = result.status === 'PASS' ? '✅' : '❌';
        const duration = result.duration ? ` (${result.duration}ms)` : '';
        console.log(`  ${status} ${result.test.replace(endpoint, '').trim()}${duration}`);
        if (result.error) {
          console.log(colorize(`      Error: ${result.error}`, 'red'));
        }
      });
    });

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        summary: { total, passed, failed, successRate },
        details: this.results,
        endpointGroups,
      };

      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run the test
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const testSuite = new OAuthEndpointsTestSuite(serverUrl);
testSuite.runEndpointsTests().catch(console.error);
