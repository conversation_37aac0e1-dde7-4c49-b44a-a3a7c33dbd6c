#!/usr/bin/env node

/**
 * OAuth Authentication Failure Tests
 *
 * Tests various OAuth failure scenarios to ensure proper error handling:
 * - Invalid client credentials
 * - Expired authorization codes
 * - Invalid PKCE code verifiers
 * - Invalid scopes
 * - Malformed requests
 * - Token validation failures
 *
 * Usage:
 *   node blackbox/tests/oauth-failure-test.js
 */

import crypto from 'crypto';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * OAuth Failure Test Suite
 * Tests various failure scenarios that should return proper OAuth error responses
 */
class OAuthFailureTestSuite {
  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.validClientId = 'test-client-' + crypto.randomBytes(8).toString('hex');
    this.validClientSecret = 'test-secret-' + crypto.randomBytes(16).toString('hex');
  }

  async runFailureTests() {
    console.log(colorize('\n🚨 OAuth Authentication Failure Tests', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Setup: Register a valid client for testing
      await this.setupValidClient();

      // Test various failure scenarios
      await this.testInvalidClientCredentials();
      await this.testMalformedAuthorizationRequests();
      await this.testInvalidPKCEParameters();
      await this.testInvalidTokenRequests();
      await this.testInvalidBearerTokens();
      await this.testScopeValidationFailures();
      await this.testRateLimitingBehavior();

      this.printResults();
      return this.results;
    } catch (error) {
      console.error(colorize(`❌ Failure test suite failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'OAuth Failure Test Suite',
        status: 'FAIL',
        error: error.message,
      });
      throw error;
    }
  }

  async setupValidClient() {
    console.log(colorize('\n🔧 Setup: Registering Valid Client', 'cyan'));

    const clientMetadata = {
      client_name: 'OAuth Failure Test Client',
      redirect_uris: ['http://localhost:8090/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_post',
      scope: 'mcp:tools read write',
    };

    const response = await this.makeRequest('/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: clientMetadata,
    });

    if (response.status === 200 || response.status === 201) {
      const clientInfo = JSON.parse(response.data);
      this.validClientId = clientInfo.client_id;
      this.validClientSecret = clientInfo.client_secret;
      console.log(colorize(`✅ Valid client registered: ${this.validClientId}`, 'green'));
    } else {
      console.log(colorize(`⚠️  Client registration failed, using default credentials`, 'yellow'));
    }
  }

  async testInvalidClientCredentials() {
    console.log(colorize('\n🔐 Test 1: Invalid Client Credentials', 'cyan'));

    const tests = [
      {
        name: 'Nonexistent Client ID',
        params: {
          client_id: 'nonexistent-client-id',
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_client',
      },
      {
        name: 'Invalid Redirect URI',
        params: {
          client_id: this.validClientId,
          response_type: 'code',
          redirect_uri: 'http://malicious.com/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_request',
      },
      {
        name: 'Wrong Client Secret in Token Request',
        endpoint: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: 'test-code',
          client_id: this.validClientId,
          client_secret: 'wrong-secret',
          code_verifier: 'test-verifier',
        }).toString(),
        expectedError: 'invalid_client',
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testMalformedAuthorizationRequests() {
    console.log(colorize('\n📝 Test 2: Malformed Authorization Requests', 'cyan'));

    const tests = [
      {
        name: 'Missing Response Type',
        params: {
          client_id: this.validClientId,
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_request',
      },
      {
        name: 'Invalid Response Type',
        params: {
          client_id: this.validClientId,
          response_type: 'token', // Only 'code' is supported
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'unsupported_response_type',
      },
      {
        name: 'Missing Client ID',
        params: {
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_request',
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testInvalidPKCEParameters() {
    console.log(colorize('\n🔑 Test 3: Invalid PKCE Parameters', 'cyan'));

    const tests = [
      {
        name: 'Missing Code Challenge',
        params: {
          client_id: this.validClientId,
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_request',
      },
      {
        name: 'Invalid Code Challenge Method',
        params: {
          client_id: this.validClientId,
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          code_challenge: 'test-challenge',
          code_challenge_method: 'plain', // Only S256 is supported
        },
        expectedError: 'invalid_request',
      },
      {
        name: 'Invalid Code Verifier in Token Exchange',
        endpoint: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: 'test-code',
          client_id: this.validClientId,
          client_secret: this.validClientSecret,
          code_verifier: 'wrong-verifier',
        }).toString(),
        expectedError: 'invalid_grant',
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testInvalidTokenRequests() {
    console.log(colorize('\n🎫 Test 4: Invalid Token Requests', 'cyan'));

    const tests = [
      {
        name: 'Missing Grant Type',
        endpoint: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          code: 'test-code',
          client_id: this.validClientId,
          client_secret: this.validClientSecret,
        }).toString(),
        expectedError: 'invalid_request',
      },
      {
        name: 'Unsupported Grant Type',
        endpoint: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'password', // Not supported
          username: 'test',
          password: 'test',
          client_id: this.validClientId,
        }).toString(),
        expectedError: 'unsupported_grant_type',
      },
      {
        name: 'Expired Authorization Code',
        endpoint: '/token',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: 'expired-code',
          client_id: this.validClientId,
          client_secret: this.validClientSecret,
          code_verifier: 'test-verifier',
        }).toString(),
        expectedError: 'invalid_grant',
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testInvalidBearerTokens() {
    console.log(colorize('\n🔒 Test 5: Invalid Bearer Tokens', 'cyan'));

    const tests = [
      {
        name: 'Invalid Bearer Token',
        endpoint: '/mcp',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer invalid-token-12345',
        },
        body: {
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test', version: '1.0.0' },
          },
        },
        expectedStatus: 401,
      },
      {
        name: 'Malformed Authorization Header',
        endpoint: '/mcp',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'InvalidFormat token-12345',
        },
        body: {
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test', version: '1.0.0' },
          },
        },
        expectedStatus: 401,
      },
      {
        name: 'Missing Authorization Header',
        endpoint: '/mcp',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: {
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'test', version: '1.0.0' },
          },
        },
        expectedStatus: 401,
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testScopeValidationFailures() {
    console.log(colorize('\n🎯 Test 6: Scope Validation Failures', 'cyan'));

    const tests = [
      {
        name: 'Invalid Scope in Authorization',
        params: {
          client_id: this.validClientId,
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          scope: 'invalid-scope admin',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_scope',
      },
      {
        name: 'Scope Not Registered for Client',
        params: {
          client_id: this.validClientId,
          response_type: 'code',
          redirect_uri: 'http://localhost:8090/callback',
          scope: 'admin superuser',
          code_challenge: 'test-challenge',
          code_challenge_method: 'S256',
        },
        expectedError: 'invalid_scope',
      },
    ];

    for (const test of tests) {
      await this.runSingleFailureTest(test);
    }
  }

  async testRateLimitingBehavior() {
    console.log(colorize('\n⏱️  Test 7: Rate Limiting Behavior', 'cyan'));

    // Test rapid requests to authorization endpoint
    const requests = [];
    const testParams = {
      client_id: 'rate-limit-test',
      response_type: 'code',
      redirect_uri: 'http://localhost:8090/callback',
      code_challenge: 'test-challenge',
      code_challenge_method: 'S256',
    };

    console.log(colorize('   Sending 10 rapid requests to test rate limiting...', 'blue'));

    for (let i = 0; i < 10; i++) {
      requests.push(this.makeRequest(`/authorize?${new URLSearchParams(testParams).toString()}`));
    }

    try {
      const responses = await Promise.all(requests);
      const rateLimited = responses.some(r => r.status === 429);

      if (rateLimited) {
        console.log(colorize(`✅ Rate limiting active: Some requests returned 429`, 'green'));
        this.results.push({
          test: 'Rate Limiting',
          status: 'PASS',
          details: { rate_limited: true },
        });
      } else {
        console.log(colorize(`⚠️  Rate limiting not detected: All requests succeeded`, 'yellow'));
        this.results.push({
          test: 'Rate Limiting',
          status: 'PARTIAL',
          details: { rate_limited: false, note: 'Rate limiting may not be configured' },
        });
      }
    } catch (error) {
      console.log(colorize(`❌ Rate limiting test failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'Rate Limiting',
        status: 'FAIL',
        error: error.message,
      });
    }
  }

  async runSingleFailureTest(test) {
    const startTime = Date.now();

    try {
      const endpoint = test.endpoint || '/authorize';
      const method = test.method || 'GET';
      const url = test.params
        ? `${endpoint}?${new URLSearchParams(test.params).toString()}`
        : endpoint;

      const response = await this.makeRequest(url, {
        method,
        headers: test.headers || {},
        body: test.body,
      });

      const duration = Date.now() - startTime;
      const expectedStatus = test.expectedStatus || (test.expectedError ? 400 : 302);

      if (response.status === expectedStatus) {
        // Check for expected error in response
        if (test.expectedError) {
          const hasExpectedError =
            response.data.includes(test.expectedError) ||
            (response.headers.location &&
              response.headers.location.includes(`error=${test.expectedError}`));

          if (hasExpectedError) {
            console.log(
              colorize(`✅ ${test.name}: Correct error response (${duration}ms)`, 'green')
            );
            this.results.push({
              test: test.name,
              status: 'PASS',
              duration,
              details: { expected_error: test.expectedError },
            });
          } else {
            console.log(
              colorize(`❌ ${test.name}: Missing expected error '${test.expectedError}'`, 'red')
            );
            this.results.push({
              test: test.name,
              status: 'FAIL',
              duration,
              error: `Expected error '${test.expectedError}' not found`,
            });
          }
        } else {
          console.log(
            colorize(`✅ ${test.name}: Correct status response (${duration}ms)`, 'green')
          );
          this.results.push({
            test: test.name,
            status: 'PASS',
            duration,
          });
        }
      } else {
        console.log(
          colorize(
            `❌ ${test.name}: Wrong status ${response.status}, expected ${expectedStatus}`,
            'red'
          )
        );
        this.results.push({
          test: test.name,
          status: 'FAIL',
          duration,
          error: `Expected status ${expectedStatus}, got ${response.status}`,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(colorize(`❌ ${test.name}: Request failed - ${error.message}`, 'red'));
      this.results.push({
        test: test.name,
        status: 'FAIL',
        duration,
        error: error.message,
      });
    }
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'User-Agent': 'oauth-failure-test/1.0.0',
          ...headers,
        },
        body: typeof body === 'string' ? body : body ? JSON.stringify(body) : undefined,
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text,
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  printResults() {
    console.log(colorize('\n📊 OAuth Failure Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (partial > 0) console.log(colorize(`Partial: ${partial}`, 'yellow'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    const successRate = Math.round(((passed + partial) / total) * 100);
    console.log(
      colorize(
        `\n🎯 Success Rate: ${successRate}%`,
        successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'
      )
    );

    // Detailed results
    console.log(colorize('\n📋 Detailed Results:', 'bold'));
    this.results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : result.status === 'PARTIAL' ? '⚠️' : '❌';
      const duration = result.duration ? ` (${result.duration}ms)` : '';
      console.log(`${status} ${result.test}${duration}`);
      if (result.error) {
        console.log(colorize(`    Error: ${result.error}`, 'red'));
      }
    });

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        summary: { total, passed, partial, failed, successRate },
        details: this.results,
      };

      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run the test
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const testSuite = new OAuthFailureTestSuite(serverUrl);
testSuite.runFailureTests().catch(console.error);
