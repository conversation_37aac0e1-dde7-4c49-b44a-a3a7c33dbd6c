#!/usr/bin/env node

/**
 * OAuth Server Behavior Test (Pure Blackbox)
 *
 * Tests OAuth server behavior without external dependencies:
 * - OAuth enabled vs disabled server behavior
 * - Authentication mode consistency
 * - OAuth endpoint availability and consistency
 * - Server configuration self-consistency
 * - Authentication behavior validation
 *
 * This is a pure blackbox test that only validates server responses
 * and internal consistency, without depending on external .env files.
 *
 * Usage:
 *   node blackbox/tests/oauth-server-blackbox-test.js
 */

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * OAuth Server Behavior Test Suite (Pure Blackbox)
 * Validates OAuth server behavior through API responses only
 */
class OAuthServerBlackboxTest {
  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.serverOAuth2Enabled = null;
    this.serverAuthConfig = null;
  }

  async makeRequest(endpoint, options = {}) {
    const url = options.baseUrl ? `${options.baseUrl}${endpoint}` : `${this.serverUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: options.method || 'GET',
        headers: options.headers || {},
        body: options.body ? JSON.stringify(options.body) : undefined,
        ...options,
      });

      const data = await response.text();
      return {
        status: response.status,
        data: data,
        headers: response.headers,
      };
    } catch (error) {
      return {
        status: 0,
        data: null,
        error: error.message,
      };
    }
  }

  async runServerTests() {
    console.log(colorize('\n⚙️  OAuth Server Behavior Tests (Pure Blackbox)', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Test server behavior without external dependencies
      await this.testOAuth2ServerBehavior();
      await this.testAuthenticationModes();
      await this.testAuthenticationBehavior();
      await this.testServerConsistency();

      this.printResults();
      return this.results;
    } catch (error) {
      console.error(colorize(`❌ Server test failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'OAuth Server Test Suite',
        status: 'FAIL',
        error: error.message,
      });
      throw error;
    }
  }

  async testOAuth2ServerBehavior() {
    console.log(colorize('\n🔐 Test 1: OAuth2 Server Behavior Validation', 'cyan'));

    // Test capabilities endpoint
    const capabilitiesResponse = await this.makeRequest('/mcp/capabilities');

    if (capabilitiesResponse.status === 200) {
      const capabilities = JSON.parse(capabilitiesResponse.data);
      const reportedOAuth2 = capabilities.oauth2_enabled;

      // Blackbox test: Just validate that server reports a consistent OAuth status
      console.log(colorize(`✅ Server reports OAuth2 status: ${reportedOAuth2}`, 'green'));
      this.results.push({
        test: 'OAuth2 Status Reporting',
        status: 'PASS',
        details: {
          oauth2_enabled: reportedOAuth2,
        },
      });

      // Store the server's reported OAuth status for consistency checks
      this.serverOAuth2Enabled = reportedOAuth2;
    } else {
      console.log(colorize(`❌ Failed to get capabilities: ${capabilitiesResponse.status}`, 'red'));
      this.results.push({
        test: 'OAuth2 Status Reporting',
        status: 'FAIL',
        error: `Capabilities endpoint failed: ${capabilitiesResponse.status}`,
      });
      return;
    }

    // Test OAuth metadata endpoint consistency with reported status
    const metadataResponse = await this.makeRequest('/.well-known/oauth-protected-resource');

    if (this.serverOAuth2Enabled) {
      // If server reports OAuth enabled, metadata should be available
      if (metadataResponse.status === 200) {
        console.log(
          colorize(`✅ OAuth metadata available (consistent with OAuth enabled)`, 'green')
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'PASS',
          details: { oauth_enabled: true, metadata_available: true },
        });
      } else {
        console.log(
          colorize(
            `❌ OAuth metadata unavailable despite OAuth being enabled: ${metadataResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'FAIL',
          error: `OAuth enabled but metadata endpoint returned ${metadataResponse.status}`,
        });
      }
    } else {
      // If server reports OAuth disabled, metadata should not be available
      if (metadataResponse.status === 404) {
        console.log(
          colorize(`✅ OAuth metadata unavailable (consistent with OAuth disabled)`, 'green')
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'PASS',
          details: { oauth_enabled: false, metadata_available: false },
        });
      } else {
        console.log(
          colorize(
            `❌ OAuth metadata available despite OAuth being disabled: ${metadataResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'OAuth Metadata Consistency',
          status: 'FAIL',
          error: `OAuth disabled but metadata endpoint returned ${metadataResponse.status}`,
        });
      }
    }
  }

  async testAuthenticationModes() {
    console.log(colorize('\n🔑 Test 2: Authentication Mode Consistency', 'cyan'));

    // Test auth status endpoint
    const authStatusResponse = await this.makeRequest('/auth/status');

    if (authStatusResponse.status === 200) {
      const authStatus = JSON.parse(authStatusResponse.data);
      const config = authStatus.config;

      // Store server's actual auth configuration for behavior testing
      this.serverAuthConfig = {
        connectionAuthEnabled: config.connectionAuthEnabled,
        perRequestAuthEnabled: config.perRequestAuthEnabled,
        connectionAuthStrict: config.connectionAuthStrict,
      };

      // Blackbox test: Validate internal consistency of auth modes
      console.log(colorize(`✅ Connection Auth Mode: ${config.connectionAuthEnabled}`, 'green'));
      this.results.push({
        test: 'Connection Auth Mode Reporting',
        status: 'PASS',
        details: { connectionAuthEnabled: config.connectionAuthEnabled },
      });

      console.log(colorize(`✅ Per-Request Auth Mode: ${config.perRequestAuthEnabled}`, 'green'));
      this.results.push({
        test: 'Per-Request Auth Mode Reporting',
        status: 'PASS',
        details: { perRequestAuthEnabled: config.perRequestAuthEnabled },
      });

      console.log(colorize(`✅ Connection Auth Strict: ${config.connectionAuthStrict}`, 'green'));
      this.results.push({
        test: 'Connection Auth Strict Reporting',
        status: 'PASS',
        details: { connectionAuthStrict: config.connectionAuthStrict },
      });

      // Validate logical consistency: if OAuth is disabled, auth modes should be disabled
      if (!this.serverOAuth2Enabled) {
        const authModesDisabled = !config.connectionAuthEnabled && !config.perRequestAuthEnabled;
        if (authModesDisabled) {
          console.log(colorize(`✅ Auth modes correctly disabled when OAuth is disabled`, 'green'));
          this.results.push({
            test: 'OAuth-Auth Mode Consistency',
            status: 'PASS',
            details: { oauth_disabled: true, auth_modes_disabled: true },
          });
        } else {
          console.log(colorize(`⚠️  Auth modes enabled despite OAuth being disabled`, 'yellow'));
          this.results.push({
            test: 'OAuth-Auth Mode Consistency',
            status: 'PARTIAL',
            note: 'Auth modes enabled despite OAuth being disabled',
          });
        }
      } else {
        console.log(colorize(`✅ OAuth enabled - auth modes configuration accepted`, 'green'));
        this.results.push({
          test: 'OAuth-Auth Mode Consistency',
          status: 'PASS',
          details: { oauth_enabled: true, auth_modes_configured: true },
        });
      }
    } else {
      console.log(colorize(`❌ Auth status endpoint failed: ${authStatusResponse.status}`, 'red'));
      this.results.push({
        test: 'Authentication Mode Reporting',
        status: 'FAIL',
        error: `Auth status endpoint failed: ${authStatusResponse.status}`,
      });
    }
  }

  async testAuthenticationBehavior() {
    console.log(colorize('\n🔒 Test 3: Authentication Behavior Consistency', 'cyan'));

    // Test a simple endpoint first to check auth behavior
    const simpleResponse = await this.makeRequest('/mcp/capabilities');

    // If simple endpoint works, test MCP request without authentication
    const noAuthResponse = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
      },
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'blackbox-test', version: '1.0.0' },
        },
      },
    });

    // Use server's actual reported configuration
    const serverAuthRequired =
      this.serverAuthConfig &&
      (this.serverAuthConfig.connectionAuthEnabled || this.serverAuthConfig.perRequestAuthEnabled);

    if (serverAuthRequired) {
      // Server reports auth is required - should return 401 or 400 without auth
      if (noAuthResponse.status === 401 || noAuthResponse.status === 400) {
        console.log(
          colorize(
            `✅ Authentication correctly required (consistent with server config): ${noAuthResponse.status}`,
            'green'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PASS',
          details: {
            server_requires_auth: true,
            response_status: noAuthResponse.status,
            consistent: true,
          },
        });
      } else {
        console.log(
          colorize(
            `❌ Authentication not enforced despite server config: ${noAuthResponse.status}`,
            'red'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'FAIL',
          error: `Server config requires auth but got ${noAuthResponse.status} instead of 401/400`,
        });
      }
    } else {
      // Server reports auth is not required - should allow access or give non-auth error
      if (noAuthResponse.status === 200) {
        console.log(
          colorize(
            `✅ No authentication required (consistent with server config): ${noAuthResponse.status}`,
            'green'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PASS',
          details: {
            server_requires_auth: false,
            response_status: noAuthResponse.status,
            consistent: true,
          },
        });
      } else if (noAuthResponse.status === 400) {
        // 400 might indicate request format issue, not auth issue - this is acceptable when auth is disabled
        console.log(
          colorize(
            `✅ Server processes request without auth (400 = request format issue, not auth): ${noAuthResponse.status}`,
            'green'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PASS',
          details: {
            server_requires_auth: false,
            response_status: noAuthResponse.status,
            consistent: true,
            note: 'Request processed without auth (400 = format issue, not auth issue)',
          },
        });
      } else if (noAuthResponse.status === 401) {
        console.log(
          colorize(
            `❌ Authentication required despite server config indicating otherwise: 401`,
            'red'
          )
        );
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'FAIL',
          error: 'Server config suggests no auth required but got 401',
        });
      } else {
        console.log(colorize(`⚠️  Unexpected response status: ${noAuthResponse.status}`, 'yellow'));
        this.results.push({
          test: 'Authentication Behavior Consistency',
          status: 'PARTIAL',
          note: `Unexpected status: ${noAuthResponse.status}`,
        });
      }
    }
  }

  async testServerConsistency() {
    console.log(colorize('\n🔍 Test 4: Server Internal Consistency', 'cyan'));

    // Test that server's OAuth status is consistent with its endpoints
    if (this.serverOAuth2Enabled !== null && this.serverAuthConfig !== null) {
      console.log(colorize(`✅ Server configuration retrieved successfully`, 'green'));
      console.log(colorize(`   OAuth2 Enabled: ${this.serverOAuth2Enabled}`, 'blue'));
      console.log(
        colorize(`   Connection Auth: ${this.serverAuthConfig.connectionAuthEnabled}`, 'blue')
      );
      console.log(
        colorize(`   Per-Request Auth: ${this.serverAuthConfig.perRequestAuthEnabled}`, 'blue')
      );
      console.log(
        colorize(`   Connection Auth Strict: ${this.serverAuthConfig.connectionAuthStrict}`, 'blue')
      );

      this.results.push({
        test: 'Server Configuration Retrieval',
        status: 'PASS',
        details: {
          oauth2_enabled: this.serverOAuth2Enabled,
          auth_config: this.serverAuthConfig,
        },
      });
    } else {
      console.log(colorize(`❌ Failed to retrieve complete server configuration`, 'red'));
      this.results.push({
        test: 'Server Configuration Retrieval',
        status: 'FAIL',
        error: 'Could not retrieve server configuration',
      });
    }
  }

  printResults() {
    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const successRate = Math.round((passed / total) * 100);

    console.log(colorize('\n📊 OAuth Server Behavior Test Results', 'bold'));
    console.log('='.repeat(60));
    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (partial > 0) console.log(colorize(`Partial: ${partial}`, 'yellow'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    console.log(
      colorize(
        `\n🎯 Success Rate: ${successRate}%`,
        successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'
      )
    );

    // Server behavior summary
    console.log(colorize('\n⚙️  Server Behavior Summary:', 'bold'));
    console.log(
      colorize(
        `OAuth2: ${this.serverOAuth2Enabled ? 'ENABLED' : 'DISABLED'}`,
        this.serverOAuth2Enabled ? 'green' : 'red'
      )
    );

    if (this.serverAuthConfig) {
      console.log(
        colorize(
          `Connection Auth: ${this.serverAuthConfig.connectionAuthEnabled ? 'ENABLED' : 'DISABLED'}`,
          this.serverAuthConfig.connectionAuthEnabled ? 'green' : 'red'
        )
      );
      console.log(
        colorize(
          `Per-Request Auth: ${this.serverAuthConfig.perRequestAuthEnabled ? 'ENABLED' : 'DISABLED'}`,
          this.serverAuthConfig.perRequestAuthEnabled ? 'green' : 'red'
        )
      );
    }

    // Detailed results
    console.log(colorize('\n📋 Detailed Results:', 'bold'));
    this.results.forEach(result => {
      const status =
        result.status === 'PASS'
          ? '✅'
          : result.status === 'PARTIAL'
            ? '⚠️'
            : result.status === 'SKIP'
              ? '⏭️'
              : '❌';
      console.log(`${status} ${result.test}`);
      if (result.note) {
        console.log(colorize(`    Note: ${result.note}`, 'yellow'));
      }
      if (result.error) {
        console.log(colorize(`    Error: ${result.error}`, 'red'));
      }
    });

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        server_config: {
          oauth2_enabled: this.serverOAuth2Enabled,
          auth_config: this.serverAuthConfig,
        },
        summary: { total, passed, partial, failed, successRate },
        details: this.results,
      };

      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run the test
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const testSuite = new OAuthServerBlackboxTest(serverUrl);
testSuite.runServerTests().catch(console.error);
