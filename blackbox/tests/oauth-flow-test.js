#!/usr/bin/env node

/**
 * Comprehensive OAuth Flow Test
 *
 * Tests the complete OAuth 2.0 Authorization Code flow with PKCE
 * as implemented in the MCP server, following the reference client pattern
 *
 * Flow tested:
 * 1. Client registration (if needed)
 * 2. Authorization request with PKCE
 * 3. Authorization code generation
 * 4. Token exchange with code verifier
 * 5. Authenticated MCP requests using Bear<PERSON> token
 * 6. Token refresh flow
 *
 * Usage:
 *   node blackbox/tests/oauth-flow-test.js
 */

import crypto from 'crypto';
import { URL } from 'url';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * PKCE (Proof Key for Code Exchange) utilities
 * Following RFC 7636 specification
 */
class PKCEUtils {
  static generateCodeVerifier() {
    return crypto.randomBytes(32).toString('base64url');
  }

  static generateCodeChallenge(verifier) {
    return crypto.createHash('sha256').update(verifier).digest('base64url');
  }

  static generateState() {
    return crypto.randomBytes(16).toString('base64url');
  }
}

/**
 * OAuth Client Simulator
 * Simulates the behavior of the reference simpleOAuthClient.ts
 */
class OAuthClientSimulator {
  constructor(serverUrl = 'http://localhost:3001') {
    this.serverUrl = serverUrl;
    this.clientMetadata = {
      client_name: 'OAuth Flow Test Client',
      redirect_uris: ['http://localhost:8090/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_post',
      scope: 'mcp:tools',
    };
    this.clientInfo = null;
    this.tokens = null;
    this.codeVerifier = null;
    this.state = null;
    this.results = [];
  }

  async runOAuthFlowTest() {
    console.log(colorize('\n🔐 Comprehensive OAuth Flow Test', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Step 1: Test OAuth metadata discovery
      await this.testOAuthMetadata();

      // Step 2: Client registration
      await this.testClientRegistration();

      // Step 3: Authorization request
      await this.testAuthorizationRequest();

      // Step 4: Simulate authorization code callback
      const authCode = await this.simulateAuthorizationCallback();

      // Step 5: Token exchange
      await this.testTokenExchange(authCode);

      // Step 6: Authenticated MCP requests
      await this.testAuthenticatedMCPRequests();

      // Step 7: Token refresh
      await this.testTokenRefresh();

      // Step 8: Token revocation
      await this.testTokenRevocation();

      this.printResults();
      return this.results;
    } catch (error) {
      console.error(colorize(`❌ OAuth flow test failed: ${error.message}`, 'red'));
      this.results.push({
        test: 'OAuth Flow',
        status: 'FAIL',
        error: error.message,
      });
      throw error;
    }
  }

  async testOAuthMetadata() {
    console.log(colorize('\n📋 Step 1: OAuth Metadata Discovery', 'cyan'));

    const startTime = Date.now();
    const response = await this.makeRequest('/.well-known/oauth-protected-resource');
    const duration = Date.now() - startTime;

    if (response.status === 200) {
      const metadata = JSON.parse(response.data);
      console.log(colorize(`✅ OAuth metadata discovered (${duration}ms)`, 'green'));
      console.log(colorize(`   Resource: ${metadata.resource}`, 'blue'));
      console.log(
        colorize(`   Authorization Server: ${metadata.authorization_servers?.[0]}`, 'blue')
      );

      this.results.push({
        test: 'OAuth Metadata Discovery',
        status: 'PASS',
        duration,
        details: metadata,
      });
    } else if (response.status === 404) {
      console.log(
        colorize(`⚠️  OAuth metadata not available (404) - OAuth may be disabled`, 'yellow')
      );
      this.results.push({
        test: 'OAuth Metadata Discovery',
        status: 'SKIP',
        duration,
        note: 'OAuth disabled or metadata endpoint not implemented',
      });
    } else {
      throw new Error(`OAuth metadata request failed: ${response.status}`);
    }
  }

  async testClientRegistration() {
    console.log(colorize('\n📝 Step 2: Client Registration', 'cyan'));

    const startTime = Date.now();
    const response = await this.makeRequest('/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: this.clientMetadata,
    });
    const duration = Date.now() - startTime;

    if (response.status === 200 || response.status === 201) {
      this.clientInfo = JSON.parse(response.data);
      console.log(colorize(`✅ Client registered successfully (${duration}ms)`, 'green'));
      console.log(colorize(`   Client ID: ${this.clientInfo.client_id}`, 'blue'));
      console.log(
        colorize(
          `   Client Secret: ${this.clientInfo.client_secret ? '[REDACTED]' : 'None (public client)'}`,
          'blue'
        )
      );

      this.results.push({
        test: 'Client Registration',
        status: 'PASS',
        duration,
        details: {
          client_id: this.clientInfo.client_id,
          has_secret: !!this.clientInfo.client_secret,
        },
      });
    } else {
      throw new Error(`Client registration failed: ${response.status} - ${response.data}`);
    }
  }

  async testAuthorizationRequest() {
    console.log(colorize('\n🔑 Step 3: Authorization Request', 'cyan'));

    // Generate PKCE parameters
    this.codeVerifier = PKCEUtils.generateCodeVerifier();
    const codeChallenge = PKCEUtils.generateCodeChallenge(this.codeVerifier);
    this.state = PKCEUtils.generateState();

    const authParams = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientInfo.client_id,
      redirect_uri: this.clientMetadata.redirect_uris[0],
      scope: this.clientMetadata.scope,
      state: this.state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    });

    const startTime = Date.now();
    const response = await this.makeRequest(`/authorize?${authParams.toString()}`);
    const duration = Date.now() - startTime;

    // Authorization endpoint should redirect or return authorization form
    if (response.status === 302) {
      const location = response.headers.location;
      console.log(colorize(`✅ Authorization redirect received (${duration}ms)`, 'green'));
      console.log(colorize(`   Redirect: ${location}`, 'blue'));

      this.results.push({
        test: 'Authorization Request',
        status: 'PASS',
        duration,
        details: { redirect_location: location },
      });
    } else if (response.status === 200) {
      // Some implementations return authorization form instead of redirect
      console.log(colorize(`✅ Authorization form received (${duration}ms)`, 'green'));
      this.results.push({
        test: 'Authorization Request',
        status: 'PASS',
        duration,
        details: { response_type: 'form' },
      });
    } else {
      throw new Error(`Authorization request failed: ${response.status} - ${response.data}`);
    }
  }

  async simulateAuthorizationCallback() {
    console.log(colorize('\n📞 Step 4: Simulate Authorization Callback', 'cyan'));

    // In a real scenario, user would authorize and be redirected back
    // For testing, we simulate receiving an authorization code
    const mockAuthCode = 'test_auth_code_' + crypto.randomBytes(16).toString('hex');

    console.log(
      colorize(`✅ Simulated authorization code: ${mockAuthCode.substring(0, 20)}...`, 'green')
    );

    this.results.push({
      test: 'Authorization Callback Simulation',
      status: 'PASS',
      details: { auth_code_length: mockAuthCode.length },
    });

    return mockAuthCode;
  }

  async testTokenExchange(authCode) {
    console.log(colorize('\n🎫 Step 5: Token Exchange', 'cyan'));

    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      code: authCode,
      redirect_uri: this.clientMetadata.redirect_uris[0],
      client_id: this.clientInfo.client_id,
      code_verifier: this.codeVerifier,
    });

    // Add client secret if available
    if (this.clientInfo.client_secret) {
      tokenParams.set('client_secret', this.clientInfo.client_secret);
    }

    const startTime = Date.now();
    const response = await this.makeRequest('/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });
    const duration = Date.now() - startTime;

    if (response.status === 200) {
      this.tokens = JSON.parse(response.data);
      console.log(colorize(`✅ Tokens received successfully (${duration}ms)`, 'green'));
      console.log(
        colorize(`   Access Token: ${this.tokens.access_token.substring(0, 20)}...`, 'blue')
      );
      console.log(colorize(`   Token Type: ${this.tokens.token_type}`, 'blue'));
      console.log(colorize(`   Expires In: ${this.tokens.expires_in}s`, 'blue'));
      console.log(
        colorize(`   Refresh Token: ${this.tokens.refresh_token ? 'Present' : 'None'}`, 'blue')
      );

      this.results.push({
        test: 'Token Exchange',
        status: 'PASS',
        duration,
        details: {
          token_type: this.tokens.token_type,
          expires_in: this.tokens.expires_in,
          has_refresh_token: !!this.tokens.refresh_token,
        },
      });
    } else {
      throw new Error(`Token exchange failed: ${response.status} - ${response.data}`);
    }
  }

  async testAuthenticatedMCPRequests() {
    console.log(colorize('\n🔒 Step 6: Authenticated MCP Requests', 'cyan'));

    if (!this.tokens) {
      throw new Error('No tokens available for authenticated requests');
    }

    // Test MCP initialize with Bearer token
    const startTime = Date.now();
    const response = await this.makeRequest('/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/event-stream',
        Authorization: `Bearer ${this.tokens.access_token}`,
      },
      body: {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'oauth-test-client', version: '1.0.0' },
        },
      },
    });
    const duration = Date.now() - startTime;

    if (response.status === 200) {
      console.log(colorize(`✅ Authenticated MCP request successful (${duration}ms)`, 'green'));

      // Try to parse Server-Sent Events response
      const lines = response.data.split('\n');
      let jsonData = null;
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            jsonData = JSON.parse(line.substring(6));
            break;
          } catch (e) {
            // Continue parsing
          }
        }
      }

      if (jsonData && jsonData.result) {
        console.log(colorize(`   Server: ${jsonData.result.serverInfo.name}`, 'blue'));
        console.log(colorize(`   Protocol: ${jsonData.result.protocolVersion}`, 'blue'));
      }

      this.results.push({
        test: 'Authenticated MCP Requests',
        status: 'PASS',
        duration,
        details: { authenticated: true },
      });
    } else if (response.status === 401) {
      throw new Error('Authentication failed - token may be invalid');
    } else {
      throw new Error(`Authenticated MCP request failed: ${response.status} - ${response.data}`);
    }
  }

  async testTokenRefresh() {
    console.log(colorize('\n🔄 Step 7: Token Refresh', 'cyan'));

    if (!this.tokens || !this.tokens.refresh_token) {
      console.log(colorize(`⏭️  Skipping token refresh - no refresh token available`, 'yellow'));
      this.results.push({
        test: 'Token Refresh',
        status: 'SKIP',
        note: 'No refresh token available',
      });
      return;
    }

    const refreshParams = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: this.tokens.refresh_token,
      client_id: this.clientInfo.client_id,
    });

    if (this.clientInfo.client_secret) {
      refreshParams.set('client_secret', this.clientInfo.client_secret);
    }

    const startTime = Date.now();
    const response = await this.makeRequest('/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: refreshParams.toString(),
    });
    const duration = Date.now() - startTime;

    if (response.status === 200) {
      const newTokens = JSON.parse(response.data);
      console.log(colorize(`✅ Token refresh successful (${duration}ms)`, 'green'));
      console.log(
        colorize(`   New Access Token: ${newTokens.access_token.substring(0, 20)}...`, 'blue')
      );

      // Update tokens
      this.tokens = newTokens;

      this.results.push({
        test: 'Token Refresh',
        status: 'PASS',
        duration,
        details: { token_refreshed: true },
      });
    } else {
      throw new Error(`Token refresh failed: ${response.status} - ${response.data}`);
    }
  }

  async testTokenRevocation() {
    console.log(colorize('\n🚫 Step 8: Token Revocation', 'cyan'));

    if (!this.tokens) {
      console.log(colorize(`⏭️  Skipping token revocation - no tokens available`, 'yellow'));
      this.results.push({
        test: 'Token Revocation',
        status: 'SKIP',
        note: 'No tokens available',
      });
      return;
    }

    const revokeParams = new URLSearchParams({
      token: this.tokens.access_token,
      client_id: this.clientInfo.client_id,
    });

    if (this.clientInfo.client_secret) {
      revokeParams.set('client_secret', this.clientInfo.client_secret);
    }

    const startTime = Date.now();
    const response = await this.makeRequest('/revoke', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: revokeParams.toString(),
    });
    const duration = Date.now() - startTime;

    if (response.status === 200 || response.status === 204) {
      console.log(colorize(`✅ Token revocation successful (${duration}ms)`, 'green'));

      // Verify token is actually revoked by trying to use it
      const verifyResponse = await this.makeRequest('/mcp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.tokens.access_token}`,
        },
        body: {
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'revocation-test', version: '1.0.0' },
          },
        },
      });

      const tokenActuallyRevoked = verifyResponse.status === 401;
      console.log(
        colorize(
          `   Token verification: ${tokenActuallyRevoked ? 'Revoked' : 'Still valid'}`,
          'blue'
        )
      );

      this.results.push({
        test: 'Token Revocation',
        status: 'PASS',
        duration,
        details: {
          revoked: true,
          verification_failed: tokenActuallyRevoked,
        },
      });
    } else if (response.status === 404) {
      console.log(colorize(`⏭️  Token revocation endpoint not available (404)`, 'yellow'));
      this.results.push({
        test: 'Token Revocation',
        status: 'SKIP',
        duration,
        note: 'Revocation endpoint not implemented',
      });
    } else {
      throw new Error(`Token revocation failed: ${response.status} - ${response.data}`);
    }
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'User-Agent': 'oauth-flow-test/1.0.0',
          ...headers,
        },
        body: typeof body === 'string' ? body : body ? JSON.stringify(body) : undefined,
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text,
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  printResults() {
    console.log(colorize('\n📊 OAuth Flow Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    if (skipped > 0) console.log(colorize(`Skipped: ${skipped}`, 'yellow'));
    if (failed > 0) console.log(colorize(`Failed: ${failed}`, 'red'));

    const successRate = Math.round(((passed + skipped) / total) * 100);
    console.log(
      colorize(
        `\n🎯 Success Rate: ${successRate}%`,
        successRate === 100 ? 'green' : successRate >= 80 ? 'yellow' : 'red'
      )
    );

    // Detailed results
    console.log(colorize('\n📋 Detailed Results:', 'bold'));
    this.results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : result.status === 'SKIP' ? '⏭️' : '❌';
      const duration = result.duration ? ` (${result.duration}ms)` : '';
      console.log(`${status} ${result.test}${duration}`);
      if (result.note) {
        console.log(colorize(`    Note: ${result.note}`, 'yellow'));
      }
    });
  }
}

// Run the test
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const simulator = new OAuthClientSimulator(serverUrl);
simulator.runOAuthFlowTest().catch(console.error);
