# MCP Server Test Suite

This directory contains a minimal, focused test suite for the Orders Portal MCP Server.

## Test Files

### 🚀 Core Tests (5 files)

1. **`quick-test.js`** - Quick health check and response time test
   - Tests basic MCP server functionality
   - Verifies response times for timeout diagnosis
   - Tests both local tools (ping/echo) and network tools (API calls)
   - **Usage**: `npm run build && node test/quick-test.js`

2. **`all-tools.js`** - Comprehensive test of all MCP tools
   - Tests all registered MCP tools with real parameters
   - Validates tool functionality and error handling
   - Generates detailed performance and success reports
   - **Usage**: `npm run build && node test/all-tools.js`

3. **`auth-cookies.js`** - Authentication verification test
   - Verifies AUTH_COOKIES are correctly transmitted
   - Tests HTTP client authentication mechanisms
   - Validates API access with session cookies
   - **Usage**: `npm run build && node test/auth-cookies.js`

4. **`quick-auth-test.js`** - 🔐 Authentication Configuration Test
   - Quick test to verify current authentication configuration
   - Tests OAuth2 enabled/disabled states without changing settings
   - Validates endpoint accessibility and auth requirements
   - **Usage**: `npm run test:quick-auth`

5. **`streamable-http-auth-suite.js`** - 🧪 Comprehensive Authentication Suite
   - Full test suite for both OAuth2 enabled and disabled scenarios
   - Automatically switches configurations and tests both modes
   - Includes server startup/shutdown and environment backup/restore
   - **Usage**: `npm run test:auth-suite`

## Test Scripts (package.json)

```bash
# Build and run basic server test
npm run test

# Quick health check (recommended for regular testing)
npm run build && node test/quick-test.js

# Test authentication cookies
npm run build && node test/auth-cookies.js

# Test all MCP tools comprehensively
npm run build && node test/all-tools.js

# 🔐 Authentication Configuration Tests
npm run test:quick-auth          # Quick auth config test
npm run test:auth-suite          # Full OAuth2 enabled/disabled suite

# 🔧 Configuration Management
npm run config:oauth2-enabled    # Enable OAuth2 + all auth
npm run config:oauth2-disabled   # Disable all authentication
npm run config:status            # Show current auth config
```

## Test Requirements

### Environment Setup

- **Required**: Valid `AUTH_COOKIES` in `.env` file for API tests
- **Optional**: `DEBUG_SERVICE_ADAPTER=true` for detailed logging

### Expected Results

- **Fast tools** (test_ping, test_echo): < 100ms
- **Network tools** (API calls): 100-500ms
- **Success rate**: 90-100% with valid authentication

## Test Coverage

✅ **Functionality** - All MCP tools work as expected  
✅ **Authentication** - Cookies transmitted correctly  
✅ **Performance** - Response time monitoring  
✅ **Error Handling** - Graceful failure modes  
✅ **Integration** - End-to-end API communication

## Troubleshooting

### Common Issues

1. **Import Errors**: Run `npm run build` before testing
2. **Auth Failures**: Update `AUTH_COOKIES` in `.env` file
3. **Slow Responses**: Check network connectivity and API endpoints
4. **Tool Not Found**: Verify tool is registered in `SERVICE_TOOL_CONFIGS`

### Test Failures

- **401 Errors**: Authentication cookies expired or invalid
- **403 Errors**: User lacks required permissions
- **Network Errors**: API endpoints unreachable
- **Timeout Errors**: Responses taking > 30 seconds

## Removed Files

The following files were removed during cleanup:

- `keycloak-token-exchange.js` (453 lines) - OAuth2/Keycloak token exchange tests
- `keycloak-token-utils.js` (315 lines) - Keycloak utility functions
- `run-keycloak-tests.js` (191 lines) - Keycloak test runner

**Note**: OAuth2 functionality is still available. See [docs/OAUTH2_GUIDE.md](../../docs/OAUTH2_GUIDE.md) for setup.

## Test Philosophy

This test suite follows a **minimal but comprehensive** approach:

- **3 focused test files** instead of 6 complex ones
- **Direct service function testing** instead of outdated wrapper functions
- **Clear, actionable output** with performance metrics
- **Real-world test scenarios** with actual API calls
- **Easy maintenance** with simple, readable code

## Quick Start

```bash
# 1. Ensure server builds successfully
npm run build

# 2. Run quick health check
node test/quick-test.js

# 3. If all tests pass, run comprehensive test
node test/all-tools.js

# 4. For auth-specific testing
node test/auth-cookies.js
```

This should give you confidence that your MCP server is working correctly!

## 🐳 Docker-Based Testing (Recommended)

### **Zero Side-Effect Testing**

The Docker test suite provides isolated, reproducible testing with no impact on your local environment.

#### **Quick Start:**

```bash
# Fast parallel tests (recommended for CI/CD)
make test-fast

# Complete test suite with cleanup
make test-full

# Clean up everything
make clean
```

#### **Available Docker Tests:**

- **`make test-fast`**: Fast parallel tests (⚡ 2-3 minutes)
- **`make test-full`**: Complete suite with cleanup (🧪 5-10 minutes)
- **`make test-oauth2-disabled`**: OAuth2 disabled tests only
- **`make test-oauth2-enabled`**: OAuth2 enabled tests only
- **`make test-client`**: MCP client simulation
- **`make test-performance`**: Performance and stress tests

#### **Docker Test Features:**

- ✅ **Zero Side Effects**: No impact on local environment
- ✅ **Parallel Execution**: OAuth2 disabled/enabled tests run simultaneously
- ✅ **Real MCP Client**: Simulates actual MCP protocol interactions
- ✅ **Comprehensive Coverage**: All authentication scenarios
- ✅ **CI/CD Ready**: GitHub Actions integration
- ✅ **Fast & Maintainable**: Optimized for speed and maintenance

#### **Test Architecture:**

```
🐳 Docker Compose Test Environment
├── mcp-server-oauth2-disabled (port 3000)
├── mcp-server-oauth2-enabled (port 3001)
├── test-runner (orchestrates all tests)
└── mcp-client-simulator (real MCP client)
```

## 🎯 Best Practices

1. **Use Docker tests for major changes** (`make test-fast`)
2. **Use quick tests for rapid verification** (`npm run test:quick-auth`)
3. **Run full suite before releases** (`make test-full`)
4. **Check test results in `test-results/`**
5. **Use CI/CD integration for automated testing**
