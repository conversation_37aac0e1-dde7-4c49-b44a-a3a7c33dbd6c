/**
 * Example: Enhanced Cookie Management System Usage
 * 
 * This example demonstrates how to use the enhanced cookie management system
 * for MCP User Tokens in various scenarios.
 */

import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { globalMcpUserTokenService } from '../src/auth/permission-service.js';
import { getAuthCookies } from '../src/auth/cookie-formatter.js';

// Example 1: Basic Cookie Usage
async function basicCookieUsage() {
  console.log('=== Example 1: Basic Cookie Usage ===');
  
  // Mock auth info (normally comes from JWT validation)
  const authInfo: AuthInfo = {
    token: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
    clientId: 'mcp-mpc-odi',
    scopes: ['profile', 'orders'],
    expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    extra: {
      sub: 'user-123',
      // McpUserToken will be added here after obtaining from permission service
    },
  };

  try {
    // Get fresh cookie string (will obtain token from permission service if needed)
    const cookieString = await globalMcpUserTokenService.getFreshCookieString(authInfo);
    console.log('Cookie string:', cookieString);
    
    // Use in HTTP requests
    const response = await fetch('https://api.example.com/orders', {
      headers: {
        'Cookie': cookieString,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('API Response status:', response.status);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Example 2: Session Information Parsing
function sessionParsingExample() {
  console.log('\n=== Example 2: Session Information Parsing ===');
  
  // Example MCP User Token (base64 encoded session string)
  const loginTime = Date.now();
  const sessionTimeout = 3600; // 1 hour
  const sessionString = `b6176ff3-03ab-420a-99eb-513849fa9e74@${loginTime}.803836ea-a648-4d23-89ab-a8b3563af3fd.${sessionTimeout}`;
  const mcpUserToken = Buffer.from(sessionString).toString('base64');
  
  try {
    // Parse session information
    const sessionInfo = globalMcpUserTokenService.parseSessionInfo(mcpUserToken);
    
    console.log('Session Info:');
    console.log('- SSO UUID:', sessionInfo.ssoUUID);
    console.log('- User ID:', sessionInfo.userId);
    console.log('- Login Time:', new Date(sessionInfo.loginTime).toISOString());
    console.log('- Session Timeout:', sessionInfo.sessionTimeout, 'seconds');
    console.log('- Expires At:', sessionInfo.expiresAt.toISOString());
    console.log('- Is Expired:', sessionInfo.isExpired);
    
    // Check expiration
    const isExpired = globalMcpUserTokenService.isTokenExpired(mcpUserToken);
    console.log('Token expired check:', isExpired);
    
  } catch (error) {
    console.error('Failed to parse session:', error);
  }
}

// Example 3: Cookie Name Determination
function cookieNamingExample() {
  console.log('\n=== Example 3: Cookie Name Determination ===');
  
  const testCases = [
    { clientId: 'mcp-mpc-odi', env: 'development' },
    { clientId: 'mcp-mpc-odi', env: 'production' },
    { clientId: 'custom-client', env: 'development' },
    { clientId: 'custom-client', env: 'production' },
    { clientId: 'orders-portal-v2', env: 'development' },
  ];
  
  testCases.forEach(({ clientId, env }) => {
    // Temporarily set environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = env;
    
    const cookieName = globalMcpUserTokenService.getCookieName(clientId);
    console.log(`${clientId} (${env}): ${cookieName}`);
    
    // Restore environment
    process.env.NODE_ENV = originalEnv;
  });
}

// Example 4: Error Handling and Token Refresh
async function errorHandlingExample() {
  console.log('\n=== Example 4: Error Handling and Token Refresh ===');
  
  const authInfo: AuthInfo = {
    token: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
    clientId: 'mcp-mpc-odi',
    scopes: ['profile'],
    expiresAt: Math.floor(Date.now() / 1000) + 3600,
    extra: { sub: 'user-123' },
  };
  
  // Simulate API call with error handling
  async function makeApiCall(url: string, authInfo: AuthInfo, retryCount = 0): Promise<any> {
    try {
      const cookieString = await globalMcpUserTokenService.getFreshCookieString(authInfo);
      
      const response = await fetch(url, {
        headers: { 'Cookie': cookieString },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
      
    } catch (error: any) {
      // Check if we should refresh the token
      if (retryCount === 0 && globalMcpUserTokenService.shouldRefreshToken(error)) {
        console.log('Auth error detected, refreshing token...');
        
        try {
          // Refresh the token
          await globalMcpUserTokenService.refreshMcpUserToken(authInfo);
          console.log('Token refreshed successfully, retrying request...');
          
          // Retry the request
          return makeApiCall(url, authInfo, retryCount + 1);
          
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          throw error; // Throw original error
        }
      }
      
      throw error;
    }
  }
  
  try {
    const result = await makeApiCall('https://api.example.com/orders', authInfo);
    console.log('API call successful:', result);
  } catch (error) {
    console.error('API call failed:', error);
  }
}

// Example 5: Using with Auth Cookies Provider
async function authCookiesProviderExample() {
  console.log('\n=== Example 5: Using with Auth Cookies Provider ===');
  
  const authInfo: AuthInfo = {
    token: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
    clientId: 'mcp-mpc-odi',
    scopes: ['profile'],
    expiresAt: Math.floor(Date.now() / 1000) + 3600,
    extra: {
      sub: 'user-123',
      // Simulate having an McpUserToken
      mcpUserToken: 'MTJhZjE1Y2EtNGUxZC00ZjQ5LWFiZGQtNTNlYTJiODlhZTM0QDE3NTU2NzI3NTA3NTUuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=',
    },
  };
  
  try {
    // Get auth cookies using the provider
    const cookies = getAuthCookies({ authInfo });
    console.log('Auth cookies from provider:', cookies);
    
    // Fallback to environment cookies if no auth info
    const fallbackCookies = getAuthCookies({ fallbackToEnv: true });
    console.log('Fallback cookies:', fallbackCookies || 'None available');
    
  } catch (error) {
    console.error('Error getting auth cookies:', error);
  }
}

// Example 6: Session Statistics and Cleanup
function sessionManagementExample() {
  console.log('\n=== Example 6: Session Management ===');
  
  // Get session statistics
  const stats = globalMcpUserTokenService.getSessionStats();
  console.log('Session Statistics:');
  console.log('- Total Sessions:', stats.totalSessions);
  console.log('- Active Sessions:', stats.activeSessions);
  
  // Manual cleanup of expired sessions
  globalMcpUserTokenService.clearExpiredSessions();
  console.log('Expired sessions cleaned up');
  
  // Updated statistics
  const updatedStats = globalMcpUserTokenService.getSessionStats();
  console.log('Updated Statistics:');
  console.log('- Total Sessions:', updatedStats.totalSessions);
  console.log('- Active Sessions:', updatedStats.activeSessions);
}

// Run all examples
async function runExamples() {
  console.log('🍪 Enhanced Cookie Management System Examples\n');
  
  try {
    await basicCookieUsage();
    sessionParsingExample();
    cookieNamingExample();
    await errorHandlingExample();
    await authCookiesProviderExample();
    sessionManagementExample();
    
    console.log('\n✅ All examples completed successfully!');
  } catch (error) {
    console.error('\n❌ Example execution failed:', error);
  }
}

// Export for use in other modules
export {
  basicCookieUsage,
  sessionParsingExample,
  cookieNamingExample,
  errorHandlingExample,
  authCookiesProviderExample,
  sessionManagementExample,
  runExamples,
};

// Run examples if this file is executed directly
if (require.main === module) {
  runExamples();
}
